{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "swiper": "^11.2.6", "vue": "^3.5.13", "vue-router": "^4.5.1", "video.js": "^8.22.0", "@fortawesome/fontawesome-free": "^6.4.0", "remixicon": "^4.5.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}