const express = require('express');
const router = express.Router();
const protect = require('../middleware/auth');
const { generateSignedUploadUrl, verifyFileExists } = require('../config/storage');
const path = require('path');

/**
 * 支援的檔案類型和大小限制
 */
const FILE_LIMITS = {
  image: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    folder: 'images'
  },
  video: {
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['video/mp4', 'video/webm', 'video/quicktime', 'video/x-msvideo'],
    folder: 'videos'
  },
  avatar: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'avatars'
  },
  cover: {
    maxSize: 20 * 1024 * 1024, // 20MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'covers'
  },
  banner: {
    maxSize: 20 * 1024 * 1024, // 20MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'banners'
  }
};

/**
 * 驗證檔案參數
 */
function validateFileParams(fileType, mimeType, fileSize) {
  const limits = FILE_LIMITS[fileType];
  if (!limits) {
    throw new Error(`不支援的檔案類型: ${fileType}`);
  }

  if (!limits.allowedTypes.includes(mimeType)) {
    throw new Error(`不支援的 MIME 類型: ${mimeType}`);
  }

  if (fileSize > limits.maxSize) {
    throw new Error(`檔案大小超過限制: ${fileSize} > ${limits.maxSize}`);
  }

  return limits;
}

/**
 * 生成唯一檔案名
 */
function generateFileName(originalName, fileType, userId) {
  const ext = path.extname(originalName);
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const limits = FILE_LIMITS[fileType];
  
  return `${limits.folder}/${timestamp}-${userId}-${random}${ext}`;
}

/**
 * 生成上傳憑證
 * POST /api/upload/signed-url
 * 
 * Body:
 * {
 *   "files": [
 *     {
 *       "name": "image.jpg",
 *       "type": "image/jpeg",
 *       "size": 1024000,
 *       "fileType": "image"
 *     }
 *   ]
 * }
 */
router.post('/signed-url', protect, async (req, res) => {
  try {
    const { files } = req.body;
    
    if (!files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({ message: '請提供檔案資訊' });
    }

    if (files.length > 9) {
      return res.status(400).json({ message: '最多只能上傳 9 個檔案' });
    }

    const uploadCredentials = [];

    for (const file of files) {
      const { name, type: mimeType, size, fileType } = file;

      if (!name || !mimeType || !size || !fileType) {
        return res.status(400).json({ 
          message: '檔案資訊不完整，需要 name, type, size, fileType' 
        });
      }

      try {
        // 驗證檔案參數
        validateFileParams(fileType, mimeType, size);

        // 生成唯一檔案名
        const fileName = generateFileName(name, fileType, req.user._id);

        // 生成 Signed URL
        const { signedUrl, publicUrl } = await generateSignedUploadUrl(
          fileName, 
          mimeType, 
          15 // 15分鐘過期
        );

        uploadCredentials.push({
          originalName: name,
          fileName,
          signedUrl,
          publicUrl,
          mimeType,
          fileType
        });

      } catch (error) {
        return res.status(400).json({ 
          message: `檔案 ${name} 驗證失敗: ${error.message}` 
        });
      }
    }

    res.json({
      message: '上傳憑證生成成功',
      credentials: uploadCredentials,
      expiresIn: 15 * 60 * 1000 // 15分鐘（毫秒）
    });

  } catch (error) {
    console.error('生成上傳憑證失敗:', error);
    res.status(500).json({ message: '生成上傳憑證失敗' });
  }
});

/**
 * 驗證檔案上傳完成
 * POST /api/upload/verify
 * 
 * Body:
 * {
 *   "files": [
 *     {
 *       "fileName": "images/1234567890-userId-abc123.jpg",
 *       "publicUrl": "https://storage.googleapis.com/bucket/file.jpg",
 *       "originalName": "image.jpg",
 *       "mimeType": "image/jpeg",
 *       "fileType": "image"
 *     }
 *   ]
 * }
 */
router.post('/verify', protect, async (req, res) => {
  try {
    const { files } = req.body;
    
    if (!files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({ message: '請提供檔案資訊' });
    }

    const verificationResults = [];

    for (const file of files) {
      const { fileName, publicUrl, originalName, mimeType, fileType } = file;

      if (!fileName || !publicUrl || !originalName || !mimeType || !fileType) {
        return res.status(400).json({ 
          message: '檔案資訊不完整' 
        });
      }

      try {
        // 驗證檔案是否存在於 GCS
        const exists = await verifyFileExists(fileName);
        
        if (!exists) {
          return res.status(400).json({ 
            message: `檔案 ${originalName} 上傳失敗或不存在` 
          });
        }

        verificationResults.push({
          fileName,
          publicUrl,
          originalName,
          mimeType,
          fileType,
          verified: true
        });

      } catch (error) {
        console.error(`驗證檔案 ${fileName} 失敗:`, error);
        return res.status(500).json({ 
          message: `驗證檔案 ${originalName} 失敗` 
        });
      }
    }

    res.json({
      message: '檔案驗證成功',
      files: verificationResults
    });

  } catch (error) {
    console.error('檔案驗證失敗:', error);
    res.status(500).json({ message: '檔案驗證失敗' });
  }
});

/**
 * 獲取支援的檔案類型和限制
 * GET /api/upload/limits
 */
router.get('/limits', (req, res) => {
  res.json({
    message: '檔案上傳限制',
    limits: FILE_LIMITS
  });
});

module.exports = router;
