<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->

<template>
  <div class="live-app bg-gray-900 min-h-screen pb-16">
    <!-- 頂部導航欄 -->
    <div class="nav-bar fixed top-0 w-full bg-gray-800 shadow-md z-50 px-4 py-3 flex items-center justify-between">
      <div class="flex items-center">
        <button class="text-pink-500 flex items-center justify-center cursor-pointer !rounded-button" @click="toggleRankingModal">
          <i class="fas fa-trophy text-xl"></i>
        </button>
      </div>

      <!-- Ranking Modal -->
      <div v-if="isRankingModalOpen" class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center" @click="toggleRankingModal">
        <div class="bg-gray-800 w-11/12 max-h-[90vh] rounded-xl overflow-hidden" @click.stop>
          <!-- Modal Header -->
          <div class="p-4 border-b border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-medium text-white">禮物排行榜</h3>
            <button class="text-gray-400 hover:text-gray-300 cursor-pointer !rounded-button" @click="toggleRankingModal">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
          
          <!-- Ranking Tabs -->
          <div class="px-4 pt-4">
            <div class="flex mb-4">
              <button 
                v-for="tab in rankingTabs" 
                :key="tab.id"
                class="flex-1 py-2 text-center text-sm font-medium cursor-pointer !rounded-button"
                :class="[activeRankingTab === tab.id ? 'text-white bg-pink-600' : 'text-gray-400 bg-gray-700']"
                @click="setActiveRankingTab(tab.id)">
                {{ tab.name }}
              </button>
            </div>
            
            <div class="flex mb-4">
              <button 
                v-for="period in rankingPeriods" 
                :key="period.id"
                class="flex-1 py-1.5 text-center text-xs font-medium cursor-pointer !rounded-button"
                :class="[activeRankingPeriod === period.id ? 'text-white bg-gray-600' : 'text-gray-400 bg-gray-700/50']"
                @click="setActiveRankingPeriod(period.id)">
                {{ period.name }}
              </button>
            </div>
          </div>
          
          <!-- Ranking List -->
          <div class="px-4 pb-4 max-h-[60vh] overflow-y-auto">
            <div class="space-y-3">
              <div v-for="(user, index) in filteredRankingList" :key="user.id" 
                class="flex items-center p-3 bg-gray-700/30 rounded-lg">
                <div class="flex items-center justify-center w-8 h-8 rounded-full mr-3"
                  :class="[
                    index === 0 ? 'bg-yellow-500' : 
                    index === 1 ? 'bg-gray-300' : 
                    index === 2 ? 'bg-amber-600' : 'bg-gray-600'
                  ]">
                  <span class="text-sm font-bold text-gray-900">{{ index + 1 }}</span>
                </div>
                <img :src="user.avatar" :alt="user.name" class="w-10 h-10 rounded-full object-cover border border-gray-600" />
                <div class="ml-3 flex-1">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-white">{{ user.name }}</p>
                    <div v-if="user.badge" class="ml-2 px-1.5 py-0.5 bg-pink-900/50 text-pink-400 text-xs rounded">
                      {{ user.badge }}
                    </div>
                  </div>
                  <p class="text-xs text-gray-400">{{ user.contribution }}</p>
                </div>
                <div class="text-right">
                  <p class="text-sm font-medium text-yellow-500">{{ formatGiftValue(user.giftValue) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 flex justify-center">
        <span class="text-lg font-bold text-white">直播星球</span>
      </div>
      <div class="flex items-center">
        <button class="mr-4 text-gray-300 cursor-pointer !rounded-button" @click="toggleSearchModal">
          <i class="fas fa-search text-lg"></i>
        </button>
        <button class="text-gray-300 cursor-pointer !rounded-button" @click="$router.push('/myprofile')">
          <i class="fas fa-user text-lg"></i>
        </button>
        <!-- Search Modal -->
        <div v-if="isSearchModalOpen" class="fixed inset-0 bg-black/80 z-50 flex items-start justify-center">
          <div class="bg-gray-800 w-full min-h-screen">
            <!-- Search Header -->
            <div class="p-4 flex items-center border-b border-gray-700">
              <div class="flex-1 relative">
                <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  v-model="searchQuery"
                  class="w-full pl-10 pr-4 py-2 bg-gray-700 rounded-full text-sm focus:outline-none border-none text-white"
                  placeholder="搜索主播、直播間或分類"
                  @input="handleSearch"
                />
              </div>
              <button class="ml-4 text-gray-300 cursor-pointer !rounded-button" @click="toggleSearchModal">
                <span class="text-sm">取消</span>
              </button>
            </div>
            <!-- Search Content -->
            <div class="p-4">
              <!-- Hot Tags -->
              <div v-if="!searchQuery" class="mb-6">
                <h3 class="text-sm text-gray-400 mb-2">熱門搜索</h3>
                <div class="flex flex-wrap gap-2">
                  <button
                    v-for="tag in hotTags"
                    :key="tag"
                    class="px-3 py-1.5 bg-gray-700 rounded-full text-sm text-gray-300 cursor-pointer !rounded-button"
                    @click="handleTagClick(tag)"
                  >
                    {{ tag }}
                  </button>
                </div>
              </div>
              <!-- Search Results -->
              <div v-else>
                <!-- Streamers -->
                <div v-if="filteredHosts.length > 0" class="mb-6">
                  <h3 class="text-sm text-gray-400 mb-2">主播</h3>
                  <div class="space-y-3">
                    <div
                      v-for="host in filteredHosts"
                      :key="host.id"
                      class="flex items-center p-2 hover:bg-gray-700 rounded-lg cursor-pointer"
                    >
                      <img :src="host.avatar" :alt="host.name" class="w-10 h-10 rounded-full object-cover" />
                      <div class="ml-3">
                        <p class="text-sm font-medium text-gray-200">{{ host.name }}</p>
                        <p class="text-xs text-gray-400">{{ host.online ? '直播中' : '離線' }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Live Rooms -->
                <div v-if="searchFilteredRooms.length > 0" class="mb-6">
                  <h3 class="text-sm text-gray-400 mb-2">直播間</h3>
                  <div class="space-y-3">
                    <div
                      v-for="room in searchFilteredRooms"
                      :key="room.id"
                      class="flex items-center p-2 hover:bg-gray-700 rounded-lg cursor-pointer"
                    >
                      <img :src="room.cover" :alt="room.title" class="w-16 h-10 rounded object-cover" />
                      <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-200 line-clamp-1">{{ room.title }}</p>
                        <p class="text-xs text-gray-400">{{ room.hostName }} · {{ formatNumber(room.viewers) }}觀看</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 主內容區域 -->
    <div class="content-area pt-14">
      <!-- 輪播Banner -->
      <div class="banner-swiper mt-2 px-3">
        <swiper
          :modules="swiperModules"
          :pagination="{ clickable: true }"
          :autoplay="{ delay: 3000, disableOnInteraction: false }"
          class="rounded-xl overflow-hidden shadow-md"
        >
          <swiper-slide v-for="(banner, index) in banners" :key="index">
            <div class="relative">
              <img :src="banner.image" alt="Banner" class="w-full h-40 object-cover" />
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                <p class="text-white font-medium text-sm">{{ banner.title }}</p>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <!-- 浮動開播按鈕 -->
      <a href="#" class="fixed bottom-20 right-4 w-14 h-14 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-500 text-white rounded-full shadow-[0_8px_30px_rgb(0,0,0,0.24)] flex items-center justify-center cursor-pointer z-40 !rounded-button hover:shadow-[0_20px_40px_rgb(0,0,0,0.24)] transition-all duration-300 transform hover:-translate-y-1">
        <i class="fas fa-video text-2xl"></i>
      </a>
      <!-- 追蹤主播列表 -->
      <div class="following-hosts mt-4">
        <div class="px-4 mb-2 flex justify-between items-center">
          <h3 class="text-gray-200 font-medium">追蹤中的主播</h3>
          <span class="text-sm text-gray-400">{{ onlineHosts.length }}/{{ hosts.length }} 在線</span>
        </div>
        <div class="hosts-container overflow-x-auto">
          <div class="hosts-wrapper flex px-4 pb-1">
            <div
              v-for="host in hosts"
              :key="host.id"
              class="host-item flex flex-col items-center mr-4 min-w-[60px]"
            >
              <div class="relative">
                <div :class="['avatar-wrapper rounded-full', host.online ? 'border-2 border-pink-500' : 'border border-gray-600']">
                  <a href="#" class="cursor-pointer">
                    <img :src="host.avatar" alt="主播頭像" class="w-12 h-12 rounded-full object-cover" />
                  </a>
                </div>
                <div v-if="host.online" class="absolute -bottom-1 right-0 bg-green-500 w-3 h-3 rounded-full border border-gray-800"></div>
              </div>
              <span class="text-xs mt-1 text-gray-300 truncate w-full text-center">{{ host.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 分類Tab欄 -->
      <div class="category-tabs sticky top-14 bg-gray-800 z-40 border-b border-gray-700 shadow-sm mt-2">
        <div class="tabs-container overflow-x-auto">
          <div class="tabs-wrapper flex px-2">
            <button
              v-for="(category, index) in categories"
              :key="index"
              :class="['tab-item px-4 py-3 text-sm font-medium whitespace-nowrap cursor-pointer !rounded-button',
              activeCategory === category.id ? 'text-pink-500 border-b-2 border-pink-500' : 'text-gray-400']"
              @click="setActiveCategory(category.id)"
            >
              {{ category.name }}
            </button>
          </div>
        </div>
      </div>
      <!-- 直播間瀑布流列表 -->
      <div class="live-rooms-container px-2 mt-2">
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="room in filteredRooms"
            :key="room.id"
            class="live-room-card bg-gray-800 rounded-lg overflow-hidden shadow-md cursor-pointer"
          >
            <div class="relative">
              <img :src="room.cover" alt="直播封面" class="w-full aspect-video object-cover" />
              <div class="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                <i class="fas fa-eye mr-1 text-xs"></i>
                <span>{{ formatNumber(room.viewers) }}</span>
              </div>
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-12"></div>
            </div>
            <div class="p-2">
              <div class="flex items-start">
                <a href="#" class="cursor-pointer">
                  <img :src="room.hostAvatar" alt="主播頭像" class="w-8 h-8 rounded-full object-cover mr-2 border border-gray-700" />
                </a>
                <div class="flex-1 overflow-hidden">
                  <h4 class="text-sm font-medium text-gray-200 truncate">{{ room.title }}</h4>
                  <div class="flex items-center mt-0.5">
                    <span class="text-xs text-gray-400 truncate">{{ room.hostName }}</span>
                    <span v-if="room.tags.length > 0" class="ml-1 px-1.5 py-0.5 bg-pink-900/50 text-pink-400 text-xs rounded">{{ room.tags[0] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 加載更多 -->
        <div class="loading-more py-4 flex justify-center">
          <p v-if="loading" class="text-gray-400 text-sm flex items-center">
            <i class="fas fa-spinner fa-spin mr-2"></i>
            正在加載更多...
          </p>
          <p v-else-if="noMoreData" class="text-gray-500 text-sm">
            沒有更多直播了
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, Autoplay } from 'swiper/modules';
import axios from 'axios';
const swiperModules = [Pagination, Autoplay];

// 會員模態框相關
const isMemberModalOpen = ref(false);
const selectedTier = ref('gold');

const membershipTiers = ref([
  {
    id: 'gold',
    name: '黃金會員',
    icon: 'fa-crown',
    price: 'NT$ 299/月',
    benefits: [
      '無廣告觀看體驗',
      '專屬黃金徽章',
      '黃金特效彈幕',
      '優先客服支援',
      '每月免費贈送 1000 鑽石'
    ]
  },
  {
    id: 'silver',
    name: '白銀會員',
    icon: 'fa-star',
    price: 'NT$ 199/月',
    benefits: [
      '無廣告觀看體驗',
      '專屬白銀徽章',
      '普通特效彈幕',
      '每月免費贈送 500 鑽石'
    ]
  },
  {
    id: 'bronze',
    name: '青銅會員',
    icon: 'fa-medal',
    price: 'NT$ 99/月',
    benefits: [
      '無廣告觀看體驗',
      '專屬青銅徽章',
      '每月免費贈送 100 鑽石'
    ]
  }
]);

const toggleMemberModal = () => {
  isMemberModalOpen.value = !isMemberModalOpen.value;
  if (isMemberModalOpen.value) {
    isRankingModalOpen.value = false;
  }
};

const selectTier = (tierId: string) => {
  selectedTier.value = tierId;
};

const purchaseMembership = () => {
  // 這裡實現購買邏輯
  alert('即將開通會員，敬請期待！');
  toggleMemberModal();
};

// 排行榜模態框相關
const isRankingModalOpen = ref(false);
const activeRankingTab = ref('audience');
const activeRankingPeriod = ref('day');

const rankingTabs = ref([
  { id: 'audience', name: '觀眾送禮' },
  { id: 'streamer', name: '主播收禮' }
]);

const rankingPeriods = ref([
  { id: 'day', name: '日榜' },
  { id: 'week', name: '週榜' },
  { id: 'month', name: '月榜' },
  { id: 'year', name: '年榜' }
]);

interface RankingUser {
  id?: string;
  name: string;
  avatar: string;
  badge?: string | null;
  contribution: string;
  giftValue: number;
}

const audienceRankingList = ref<RankingUser[]>([]);
const streamerRankingList = ref<RankingUser[]>([]);
const hotTags = ref<string[]>([]);

const toggleRankingModal = () => {
  isRankingModalOpen.value = !isRankingModalOpen.value;
  if (isRankingModalOpen.value) {
    isMemberModalOpen.value = false;
  }
};

const setActiveRankingTab = (tabId: string) => {
  activeRankingTab.value = tabId;
};

const setActiveRankingPeriod = (periodId: string) => {
  activeRankingPeriod.value = periodId;
};

const filteredRankingList = computed(() => {
  return activeRankingTab.value === 'audience' ? audienceRankingList.value : streamerRankingList.value;
});

const formatGiftValue = (value: number): string => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '萬';
  }
  return value.toString();
};

// 當前選中的分類
const activeCategory = ref('all');
// 是否正在加載
const loading = ref(false);
const noMoreData = ref(false);
// 設置當前分類
const setActiveCategory = (categoryId: string) => {
  activeCategory.value = categoryId;
  nextTick(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  });
};

// Search related
const isSearchModalOpen = ref(false);
const searchQuery = ref('');
const toggleSearchModal = () => {
  isSearchModalOpen.value = !isSearchModalOpen.value;
  if (!isSearchModalOpen.value) {
    searchQuery.value = '';
  }
};
const handleSearch = () => {
  // Implement real-time search logic here
};
const handleTagClick = (tag: string) => {
  searchQuery.value = tag;
};
const filteredHosts = computed(() => {
  if (!searchQuery.value) return [];
  const query = searchQuery.value.toLowerCase();
  return hosts.value.filter(h => {
    const name = h?.name ?? '';
    return name.toLowerCase().includes(query);
  });
});
const searchFilteredRooms = computed(() => {
  if (!searchQuery.value) return [];
  const query = searchQuery.value.toLowerCase();
  return rooms.value.filter(r => {
    const title = (r?.title ?? '').toLowerCase();
    const hostName = (r?.hostName ?? '').toLowerCase();
    const tagsArr = Array.isArray(r?.tags) ? r.tags : [];
    return (
      title.includes(query) ||
      hostName.includes(query) ||
      tagsArr.some(t => (t ?? '').toLowerCase().includes(query))
    );
  });
});
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '萬';
  }
  return num.toString();
};
// 輪播Banner數據
const banners = ref<any[]>([]);
// 分類數據
const categories = ref<any[]>([]);
// 主播數據
const hosts = ref<any[]>([]);
// 計算在線主播
const onlineHosts = computed(() => {
  return hosts.value.filter(host => host.online);
});
// 直播間數據
const rooms = ref<any[]>([]);
// 後端已依分類過濾，前端直接使用
const filteredRooms = computed(() => rooms.value);
// 分頁狀態
const page = ref(1);
const loadMoreRooms = async () => {
  if (loading.value || noMoreData.value) return;
  loading.value = true;
  page.value += 1;
  try {
    const { data } = await axios.get('/live-rooms', {
      params: { category: activeCategory.value, page: page.value, limit: 20 },
    });
    if (data.length === 0) {
      noMoreData.value = true;
    } else {
      rooms.value = [...rooms.value, ...data];
    }
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};
// 取得排行榜
const fetchRankings = async () => {
  try {
    const [audRes, strRes] = await Promise.all([
      axios.get('/rankings/audience', { params: { period: activeRankingPeriod.value } }),
      axios.get('/rankings/streamer', { params: { period: activeRankingPeriod.value } }),
    ]);
    audienceRankingList.value = audRes.data;
    streamerRankingList.value = strRes.data;
  } catch (err) {
    console.error(err);
  }
};

// 熱門標籤
const fetchHotTags = async () => {
  try {
    const { data } = await axios.get('/tags/hot');
    hotTags.value = data;
  } catch (err) {
    console.error(err);
  }
};

// 監聽滾動事件，實現無限加載
onMounted(() => {
  fetchBanners();
  fetchCategories();
  fetchFollowingHosts();
  fetchRooms();
  fetchRankings();
  fetchHotTags();
  window.addEventListener('scroll', () => {
    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = document.documentElement.scrollTop;
    const clientHeight = document.documentElement.clientHeight;
    // 當滾動到底部時加載更多
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      loadMoreRooms();
    }
  });
});

// 當分類變動時重新撈取直播間
watch(activeCategory, () => {
  fetchRooms();
});

// 重新撈取排行榜 period or tab change
watch([activeRankingPeriod, activeRankingTab], () => {
  fetchRankings();
});

// -------------- API 呼叫函式 --------------
const fetchBanners = async () => {
  try {
    const { data } = await axios.get('/banners');
    banners.value = data;
  } catch (err) {
    console.error(err);
  }
};

const fetchCategories = async () => {
  try {
    const { data } = await axios.get('/categories');
    categories.value = [{ id: 'all', name: '全部' }, ...data.map(c => ({ id: c.slug, name: c.name }))];
  } catch (err) {
    console.error(err);
  }
};

const fetchFollowingHosts = async () => {
  const token = localStorage.getItem('token');
  if (!token) return;
  try {
    const { data } = await axios.get('/hosts/following', {
      headers: { Authorization: `Bearer ${token}` },
    });
    // 後端目前僅回傳 name / avatar，這裡隨機給 online 狀態做示意
    hosts.value = data.map(h => ({ ...h, online: Math.random() < 0.5 }));
  } catch (err) {
    console.error(err);
  }
};

const fetchRooms = async () => {
  try {
    const { data } = await axios.get('/live-rooms', {
      params: { category: activeCategory.value },
    });
    rooms.value = data;
  } catch (err) {
    console.error(err);
  }
};
</script>
<style scoped>
.hosts-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.hosts-container::-webkit-scrollbar {
  display: none;
}
.tabs-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.tabs-container::-webkit-scrollbar {
  display: none;
}
.avatar-wrapper {
  padding: 2px;
  background-color: #1f2937;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.fa-spinner {
  animation: spin 1s linear infinite;
}
</style>

