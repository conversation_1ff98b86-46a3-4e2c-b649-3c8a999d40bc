# CORS 問題解決方案

## 🚨 問題描述

當前端嘗試直接上傳檔案到 Google Cloud Storage 時，遇到 CORS 錯誤：

```
Access to XMLHttpRequest at 'https://storage.googleapis.com/liveshowsotime/...' 
from origin 'https://liveshow.sotime.app' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 🔧 解決方案

### 方案 1：設定 GCS Bucket CORS 政策（推薦）

#### 步驟 1：執行 CORS 設定腳本

```bash
# 在伺服器上執行
node setup-gcs-cors.js
```

#### 步驟 2：使用 gcloud CLI 設定（替代方案）

如果有 gcloud CLI 訪問權限：

```bash
# 設定 CORS
gsutil cors set gcs-cors-config.json gs://liveshowsotime

# 驗證設定
gsutil cors get gs://liveshowsotime
```

#### 步驟 3：驗證 CORS 設定

設定完成後，可以測試 CORS：

```bash
curl -H "Origin: https://liveshow.sotime.app" \
     -H "Access-Control-Request-Method: PUT" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://storage.googleapis.com/liveshowsotime/test
```

### 方案 2：暫時回退到傳統上傳（已實施）

在 CORS 設定完成之前，已暫時修改代碼回到傳統上傳方式：

- ✅ 保留檔案壓縮功能
- ✅ 通過 Server 上傳到 GCS
- ✅ 維持現有功能正常運作

## 📋 CORS 設定詳情

### 允許的來源（Origins）
- `https://liveshow.sotime.app` - 生產環境
- `http://localhost:5173` - 開發環境（Vite）
- `http://localhost:6666` - 本地測試環境

### 允許的方法（Methods）
- `GET` - 讀取檔案
- `HEAD` - 檢查檔案存在
- `PUT` - 上傳檔案
- `POST` - 創建檔案
- `DELETE` - 刪除檔案
- `OPTIONS` - CORS 預檢請求

### 允許的標頭（Headers）
- `Content-Type` - 檔案類型
- `Access-Control-Allow-Origin` - CORS 來源
- `Access-Control-Allow-Methods` - CORS 方法
- `Access-Control-Allow-Headers` - CORS 標頭
- `Access-Control-Max-Age` - CORS 快取時間

## 🔄 切換回直接上傳

CORS 設定完成後，可以切換回直接上傳模式：

### 步驟 1：恢復直接上傳代碼

```typescript
// 在 Wall.vue 的 createPost 函數中
// 將傳統上傳代碼替換為直接上傳代碼
```

### 步驟 2：測試直接上傳

1. 選擇檔案
2. 檢查 Network 標籤
3. 確認直接上傳到 GCS 成功
4. 驗證沒有 CORS 錯誤

## 🧪 測試步驟

### 1. 測試當前功能（傳統上傳）
- ✅ 檔案壓縮正常
- ✅ 上傳通過 Server
- ✅ 發文功能正常

### 2. 設定 CORS 後測試（直接上傳）
- [ ] 獲取上傳憑證
- [ ] 直接上傳到 GCS
- [ ] 無 CORS 錯誤
- [ ] 上傳速度提升

## 📊 效能比較

### 傳統上傳（當前）
```
前端 → Server → GCS
- 檔案傳輸兩次
- Server 處理負擔
- 較慢的上傳速度
```

### 直接上傳（CORS 設定後）
```
前端 → GCS（直接）
- 檔案只傳輸一次
- Server 負擔輕
- 更快的上傳速度
```

## 🔧 故障排除

### 常見問題

1. **CORS 設定未生效**
   - 等待 5-10 分鐘讓設定生效
   - 清除瀏覽器快取
   - 檢查 bucket 權限

2. **權限不足**
   - 確認服務帳戶有 Storage Admin 權限
   - 檢查 GCP 專案設定

3. **設定檔案路徑錯誤**
   - 確認 `config/ccw-dev-81e940b67f37.json` 存在
   - 檢查環境變數設定

## 📝 執行清單

- [x] 創建 CORS 設定檔案
- [x] 創建 CORS 設定腳本
- [x] 暫時回退到傳統上傳
- [ ] 執行 CORS 設定腳本
- [ ] 驗證 CORS 設定
- [ ] 測試直接上傳
- [ ] 切換回直接上傳模式

## 🚀 下一步

1. **立即執行**：
   ```bash
   node setup-gcs-cors.js
   ```

2. **驗證設定**：
   - 檢查控制台輸出
   - 測試 CORS 請求

3. **切換模式**：
   - 恢復直接上傳代碼
   - 測試完整流程

4. **監控效能**：
   - 比較上傳速度
   - 監控錯誤率
