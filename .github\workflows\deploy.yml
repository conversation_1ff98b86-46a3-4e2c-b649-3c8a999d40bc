name: Deploy SoTime App

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

permissions:
  contents: read
  packages: write

env:
  DOCKER_IMAGE_NAME: sotime-app
  CONTAINER_NAME: sotime-app

jobs:
  test:
    runs-on: self-hosted
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install backend dependencies
      run: npm install

    - name: Install frontend dependencies
      run: cd client && npm install --legacy-peer-deps

    - name: Run backend tests (if available)
      run: npm test --if-present

    - name: Build frontend
      run: cd client && npm run build

    - name: Lint check
      run: |
        npm run lint --if-present
        cd client && npm run lint --if-present

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest
          ghcr.io/${{ github.repository_owner }}/liveshowwebapp:${{ github.sha }}

  deploy:
    needs: build-and-deploy
    runs-on: self-hosted
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

    steps:
    - name: Deploy to server
      run: |
        echo "🚀 开始部署到服务器..."

        # 检查 Docker 是否可用
        if ! command -v docker &> /dev/null; then
          echo "❌ Docker 未安装，请先安装 Docker"
          echo "安装命令："
          echo "sudo apt update && sudo apt install -y docker.io"
          echo "sudo systemctl start docker"
          echo "sudo usermod -aG docker \$USER"
          exit 1
        fi

        # 登录到 GitHub Container Registry
        echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

        # 停止现有容器
        docker stop sotime-app || true
        docker rm sotime-app || true

        # 拉取最新镜像
        docker pull ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest

        # 启动新容器
        docker run -d \
          --name sotime-app \
          --restart unless-stopped \
          -p 8888:6666 \
          -e NODE_ENV=production \
          -e MONGO_URI=*************************************************************************** \
          -e JWT_SECRET=sayhong \
          -e GCP_PROJECT_ID=ccw-dev \
          -e GCP_BUCKET_NAME=liveshowsotime \
          ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest

        # 等待容器启动
        sleep 10

        # 健康检查
        if curl -f http://localhost:8888/health; then
          echo "✅ 部署成功！应用正在运行在端口 8888"
        else
          echo "❌ 健康检查失败，查看容器日志："
          docker logs sotime-app
          exit 1
        fi

        # 清理旧镜像
        docker image prune -f

  notify:
    needs: [test, build-and-deploy, deploy]
    runs-on: self-hosted
    if: always()
    
    steps:
    - name: Notify deployment status
      continue-on-error: true
      run: |
        if [ -n "${{ secrets.WEBHOOK_URL }}" ]; then
          if [ "${{ needs.build-and-deploy.result }}" == "success" ]; then
            STATUS="✅ 部署成功"
            COLOR="good"
          else
            STATUS="❌ 部署失敗"
            COLOR="danger"
          fi

          curl -X POST "${{ secrets.WEBHOOK_URL }}" \
            -H 'Content-Type: application/json' \
            -d "{
              \"text\": \"SoTime App 部署通知\",
              \"attachments\": [{
                \"color\": \"$COLOR\",
                \"fields\": [{
                  \"title\": \"狀態\",
                  \"value\": \"$STATUS\",
                  \"short\": true
                }, {
                  \"title\": \"分支\",
                  \"value\": \"${{ github.ref_name }}\",
                  \"short\": true
                }, {
                  \"title\": \"提交\",
                  \"value\": \"${{ github.sha }}\",
                  \"short\": true
                }]
              }]
            }"
        else
          echo "📢 通知跳过：未配置 WEBHOOK_URL"
        fi

    - name: Deployment completed
      run: |
        echo "🎉 部署流程完成！"
        echo "✅ 测试通过"
        echo "✅ 构建成功"
        echo "✅ Docker 镜像已推送到 GitHub Container Registry"
        echo ""
        echo "📦 镜像地址: ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest"
        echo "🌐 本地访问: http://localhost:8888"
        echo "🚀 服务器部署: docker pull ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest && docker run -d -p 8888:6666 --name sotime-app ghcr.io/${{ github.repository_owner }}/liveshowwebapp:latest"
