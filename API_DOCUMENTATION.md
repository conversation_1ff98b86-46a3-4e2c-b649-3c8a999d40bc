# API 文檔 - 文件上傳功能

## 概述
所有文件上傳功能已遷移到 Google Cloud Storage (GCS)。上傳的文件將存儲在 `liveshowsotime` bucket 中，並返回公開可訪問的 URL。

## 認證
大部分 API 需要 JWT 認證。在請求頭中包含：
```
Authorization: Bearer <your_jwt_token>
```

## 文件上傳 API

### 1. 貼文媒體上傳

#### 創建貼文
- **URL**: `POST /api/posts`
- **認證**: 需要
- **Content-Type**: `multipart/form-data`
- **參數**:
  - `content` (string): 貼文內容
  - `media` (files): 媒體文件（最多9個）
- **文件限制**: 100MB，支援圖片和影片
- **返回**: 創建的貼文對象，包含 GCS URL

#### 編輯貼文
- **URL**: `PUT /api/posts/:id`
- **認證**: 需要（僅作者）
- **Content-Type**: `multipart/form-data`
- **參數**:
  - `content` (string): 更新的內容
  - `media` (files): 新增的媒體文件
  - `removeMedia` (string): JSON 字符串，要刪除的媒體 ID 數組

### 2. 用戶頭像上傳

#### 上傳頭像
- **URL**: `POST /api/auth/avatar`
- **認證**: 需要
- **Content-Type**: `multipart/form-data`
- **參數**:
  - `avatar` (file): 頭像圖片文件
- **文件限制**: 10MB，僅支援圖片
- **返回**: 更新後的用戶信息

#### 更新用戶資料
- **URL**: `PUT /api/auth/profile`
- **認證**: 需要
- **Content-Type**: `application/json`
- **參數**:
  - `name` (string): 用戶姓名
  - `username` (string): 用戶名

### 3. 直播間管理

#### 創建直播間
- **URL**: `POST /api/live-rooms`
- **認證**: 需要
- **Content-Type**: `multipart/form-data`
- **參數**:
  - `title` (string): 直播間標題
  - `category` (string): 分類 ID
  - `tags` (string/array): 標籤
  - `cover` (file): 封面圖片
- **文件限制**: 20MB，僅支援圖片

#### 更新直播間
- **URL**: `PUT /api/live-rooms/:id`
- **認證**: 需要（僅主播）
- **Content-Type**: `multipart/form-data`
- **參數**: 同創建直播間

#### 刪除直播間
- **URL**: `DELETE /api/live-rooms/:id`
- **認證**: 需要（僅主播）

### 4. Banner 管理

#### 創建 Banner
- **URL**: `POST /api/banners`
- **認證**: 需要（管理員）
- **Content-Type**: `multipart/form-data`
- **參數**:
  - `title` (string): Banner 標題
  - `link` (string): 點擊連結
  - `order` (number): 排序順序
  - `image` (file): Banner 圖片
- **文件限制**: 20MB，僅支援圖片

#### 更新 Banner
- **URL**: `PUT /api/banners/:id`
- **認證**: 需要（管理員）
- **Content-Type**: `multipart/form-data`

#### 刪除 Banner
- **URL**: `DELETE /api/banners/:id`
- **認證**: 需要（管理員）

## 文件 URL 格式
所有上傳的文件都會返回以下格式的 URL：
```
https://storage.googleapis.com/liveshowsotime/{filename}
```

### 文件命名規則
- 貼文媒體: `{timestamp}-media.{ext}`
- 用戶頭像: `avatars/{timestamp}-{userId}.{ext}`
- 直播間封面: `covers/{timestamp}-{userId}.{ext}`
- Banner 圖片: `banners/{timestamp}-banner.{ext}`

## 錯誤處理
- **400**: 請求參數錯誤
- **401**: 未認證或認證失敗
- **403**: 權限不足
- **404**: 資源不存在
- **500**: 服務器內部錯誤

## 使用範例

### JavaScript (前端)
```javascript
// 上傳頭像
const formData = new FormData();
formData.append('avatar', file);

const response = await fetch('/api/auth/avatar', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
console.log('頭像 URL:', result.user.avatar);
```

### cURL
```bash
# 上傳頭像
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "avatar=@avatar.jpg" \
  http://localhost:5003/api/auth/avatar
```
