const express = require('express');
const router = express.Router();
const Banner = require('../models/Banner');
const protect = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const { uploadToGCS, deleteFromGCS, getFileNameFromUrl } = require('../config/storage');

// 檔案上傳設定 - 使用記憶體存儲
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB 限制（Banner 圖片）
  },
  fileFilter: (req, file, cb) => {
    // 只允許圖片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳圖片文件'), false);
    }
  }
});

// 取得所有 Banner，依 order 排序
router.get('/', async (req, res) => {
  try {
    const banners = await Banner.find().sort({ order: 1 });
    res.json(banners);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得 Banner 失敗' });
  }
});

// 創建 Banner（需要管理員權限）
router.post('/', protect, upload.single('image'), async (req, res) => {
  const { title, link, order } = req.body;

  try {
    // 這裡可以添加管理員權限檢查
    // if (!req.user.isAdmin) return res.status(403).json({ message: '需要管理員權限' });

    if (!req.file) {
      return res.status(400).json({ message: '請上傳 Banner 圖片' });
    }

    // 上傳圖片到 GCS
    const ext = path.extname(req.file.originalname);
    const fileName = `banners/${Date.now()}-banner${ext}`;
    const imageUrl = await uploadToGCS(req.file.buffer, fileName, req.file.mimetype);

    const banner = await Banner.create({
      title,
      image: imageUrl,
      link: link || '',
      order: order || 0
    });

    res.status(201).json(banner);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '創建 Banner 失敗' });
  }
});

// 更新 Banner
router.put('/:id', protect, upload.single('image'), async (req, res) => {
  const { title, link, order } = req.body;

  try {
    // 這裡可以添加管理員權限檢查
    // if (!req.user.isAdmin) return res.status(403).json({ message: '需要管理員權限' });

    const banner = await Banner.findById(req.params.id);
    if (!banner) return res.status(404).json({ message: 'Banner 不存在' });

    // 如果有上傳新圖片
    if (req.file) {
      // 刪除舊圖片（如果存在且是 GCS URL）
      if (banner.image && banner.image.includes('storage.googleapis.com')) {
        try {
          const oldFileName = getFileNameFromUrl(banner.image);
          await deleteFromGCS(oldFileName);
        } catch (deleteError) {
          console.error('刪除舊 Banner 圖片失敗:', deleteError);
          // 繼續執行，不中斷流程
        }
      }

      const ext = path.extname(req.file.originalname);
      const fileName = `banners/${Date.now()}-banner${ext}`;
      banner.image = await uploadToGCS(req.file.buffer, fileName, req.file.mimetype);
    }

    // 更新其他字段
    if (title !== undefined) banner.title = title;
    if (link !== undefined) banner.link = link;
    if (order !== undefined) banner.order = order;

    await banner.save();
    res.json(banner);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '更新 Banner 失敗' });
  }
});

// 刪除 Banner
router.delete('/:id', protect, async (req, res) => {
  try {
    // 這裡可以添加管理員權限檢查
    // if (!req.user.isAdmin) return res.status(403).json({ message: '需要管理員權限' });

    const banner = await Banner.findById(req.params.id);
    if (!banner) return res.status(404).json({ message: 'Banner 不存在' });

    // 刪除圖片（如果存在且是 GCS URL）
    if (banner.image && banner.image.includes('storage.googleapis.com')) {
      try {
        const fileName = getFileNameFromUrl(banner.image);
        await deleteFromGCS(fileName);
      } catch (deleteError) {
        console.error('刪除 Banner 圖片失敗:', deleteError);
        // 繼續執行，不中斷流程
      }
    }

    await Banner.deleteOne({ _id: req.params.id });
    res.json({ message: 'Banner 已刪除' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '刪除 Banner 失敗' });
  }
});

module.exports = router;