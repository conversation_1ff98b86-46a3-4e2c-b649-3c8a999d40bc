#!/bin/bash

# SoTime App 部署腳本
# 使用方法: ./deploy.sh [環境] [操作]
# 環境: dev, staging, prod
# 操作: build, start, stop, restart, logs, clean

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
ENVIRONMENT=${1:-prod}
ACTION=${2:-start}
APP_NAME="sotime-app"
COMPOSE_FILE="docker-compose.yml"

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 和 Docker Compose
check_dependencies() {
    log_info "檢查依賴..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    log_success "依賴檢查完成"
}

# 構建應用
build_app() {
    log_info "構建 Docker 映像..."
    
    # 構建映像
    docker build -t ${APP_NAME}:latest .
    docker tag ${APP_NAME}:latest ${APP_NAME}:$(date +%Y%m%d_%H%M%S)
    
    log_success "映像構建完成"
}

# 啟動服務
start_services() {
    log_info "啟動服務..."
    
    # 創建必要的目錄
    mkdir -p logs uploads
    
    # 複製環境配置
    if [ -f ".env.${ENVIRONMENT}" ]; then
        cp .env.${ENVIRONMENT} .env
        log_info "使用 ${ENVIRONMENT} 環境配置"
    else
        log_warning "未找到 .env.${ENVIRONMENT} 文件，使用默認配置"
    fi
    
    # 啟動服務
    docker-compose -f ${COMPOSE_FILE} up -d
    
    # 等待服務啟動
    log_info "等待服務啟動..."
    sleep 30
    
    # 健康檢查
    if health_check; then
        log_success "服務啟動成功！"
        show_status
    else
        log_error "服務啟動失敗！"
        docker-compose logs
        exit 1
    fi
}

# 停止服務
stop_services() {
    log_info "停止服務..."
    docker-compose -f ${COMPOSE_FILE} down
    log_success "服務已停止"
}

# 重啟服務
restart_services() {
    log_info "重啟服務..."
    stop_services
    start_services
}

# 查看日誌
show_logs() {
    log_info "顯示服務日誌..."
    docker-compose -f ${COMPOSE_FILE} logs -f --tail=100
}

# 清理資源
clean_resources() {
    log_warning "清理 Docker 資源..."
    
    # 停止服務
    docker-compose -f ${COMPOSE_FILE} down -v
    
    # 清理未使用的映像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的網絡
    docker network prune -f
    
    log_success "清理完成"
}

# 健康檢查
health_check() {
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:6666/health &> /dev/null; then
            return 0
        fi
        
        log_info "健康檢查失敗，重試 ($attempt/$max_attempts)..."
        sleep 5
        ((attempt++))
    done
    
    return 1
}

# 顯示狀態
show_status() {
    log_info "服務狀態:"
    docker-compose -f ${COMPOSE_FILE} ps
    
    echo ""
    log_info "訪問地址:"
    echo "  🌐 應用: http://localhost:6666"
    echo "  🌐 正式機: https://liveshow.sotime.app"
    echo "  📊 健康檢查: http://localhost:6666/health"
    echo "  🗄️ MongoDB: localhost:27017"
    echo "  🔴 Redis: localhost:6379"
}

# 備份數據
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p ${backup_dir}
    
    log_info "備份數據到 ${backup_dir}..."
    
    # 備份 MongoDB
    docker-compose exec -T mongodb mongodump --out /tmp/backup
    docker cp $(docker-compose ps -q mongodb):/tmp/backup ${backup_dir}/mongodb

    log_info "注意：文件現在存儲在 GCP Storage，無需本地備份"

    log_success "數據備份完成"
}

# 主函數
main() {
    log_info "SoTime App 部署腳本"
    log_info "環境: ${ENVIRONMENT}, 操作: ${ACTION}"
    
    check_dependencies
    
    case ${ACTION} in
        build)
            build_app
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_resources
            ;;
        status)
            show_status
            ;;
        backup)
            backup_data
            ;;
        *)
            log_error "未知操作: ${ACTION}"
            echo "可用操作: build, start, stop, restart, logs, clean, status, backup"
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"
