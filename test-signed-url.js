const { generateSignedUploadUrl } = require('./config/storage');

async function testSignedUrl() {
  const { default: fetch } = await import('node-fetch');
  try {
    console.log('🧪 測試 Signed URL 生成和上傳...');
    
    // 1. 生成 Signed URL
    const fileName = `test-${Date.now()}.txt`;
    const mimeType = 'text/plain';
    
    const { signedUrl, publicUrl } = await generateSignedUploadUrl(fileName, mimeType, 15);
    
    console.log('✅ Signed URL 生成成功:');
    console.log('檔案名稱:', fileName);
    console.log('MIME 類型:', mimeType);
    console.log('Signed URL:', signedUrl.substring(0, 100) + '...');
    console.log('Public URL:', publicUrl);
    
    // 2. 測試上傳
    console.log('\n🔄 測試檔案上傳...');
    
    const testContent = 'Hello, this is a test file for GCS direct upload!';
    
    const uploadResponse = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': mimeType,
      },
      body: testContent
    });
    
    console.log('上傳回應狀態:', uploadResponse.status);
    console.log('上傳回應標頭:', Object.fromEntries(uploadResponse.headers.entries()));
    
    if (uploadResponse.ok) {
      console.log('✅ 檔案上傳成功！');
      
      // 3. 驗證檔案是否存在
      console.log('\n🔍 驗證檔案是否存在...');
      
      const verifyResponse = await fetch(publicUrl, {
        method: 'HEAD'
      });
      
      if (verifyResponse.ok) {
        console.log('✅ 檔案驗證成功！檔案已存在於 GCS');
      } else {
        console.log('❌ 檔案驗證失敗，狀態碼:', verifyResponse.status);
      }
      
    } else {
      console.log('❌ 檔案上傳失敗');
      const errorText = await uploadResponse.text();
      console.log('錯誤內容:', errorText);
    }
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
}

// 執行測試
testSignedUrl();
