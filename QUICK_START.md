# 🚀 SoTime App 快速啟動指南

## 一鍵部署 (推薦)

### Windows 用戶
```cmd
# 啟動服務
deploy.bat prod start

# 查看狀態
deploy.bat prod status

# 查看日誌
deploy.bat prod logs
```

### Linux/Mac 用戶
```bash
# 設置執行權限
chmod +x deploy.sh

# 啟動服務
./deploy.sh prod start

# 查看狀態
./deploy.sh prod status

# 查看日誌
./deploy.sh prod logs
```

## 手動部署

### 1. 構建並啟動
```bash
# 構建 Docker 映像
docker build -t sotime-app:latest .

# 啟動所有服務
docker-compose up -d
```

### 2. 驗證部署
```bash
# 檢查服務狀態
docker-compose ps

# 健康檢查
curl http://localhost:6666/health

# 查看日誌
docker-compose logs -f
```

## 🌐 訪問應用

部署成功後，您可以通過以下地址訪問：

- **主應用**: http://localhost:6666
- **健康檢查**: http://localhost:6666/health
- **API 文檔**: http://localhost:6666/api

## 🔧 常用操作

### 重啟服務
```bash
# Windows
deploy.bat prod restart

# Linux/Mac
./deploy.sh prod restart
```

### 停止服務
```bash
# Windows
deploy.bat prod stop

# Linux/Mac
./deploy.sh prod stop
```

### 查看日誌
```bash
# Windows
deploy.bat prod logs

# Linux/Mac
./deploy.sh prod logs
```

### 清理資源
```bash
# Windows
deploy.bat prod clean

# Linux/Mac
./deploy.sh prod clean
```

## 🛠️ 故障排除

### 端口被占用
```bash
# 查看端口使用情況
netstat -ano | findstr :6666

# 停止占用端口的進程
taskkill /PID <PID> /F
```

### 服務無法啟動
```bash
# 查看詳細日誌
docker-compose logs

# 重新構建映像
docker build --no-cache -t sotime-app:latest .

# 清理並重新啟動
docker-compose down -v
docker-compose up -d
```

### 數據庫連接問題
```bash
# 檢查 MongoDB 容器
docker-compose logs mongodb

# 重啟 MongoDB
docker-compose restart mongodb
```

## 📊 監控

### 查看資源使用
```bash
# 實時監控
docker stats

# 查看容器狀態
docker-compose ps
```

### 健康檢查
```bash
# 應用健康狀態
curl http://localhost:6666/health

# 數據庫狀態
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"
```

## 🔄 更新應用

### 從 Git 更新
```bash
# 拉取最新代碼
git pull origin main

# 重新構建和部署
docker build -t sotime-app:latest .
docker-compose up -d --force-recreate
```

### 手動更新
```bash
# 停止服務
docker-compose down

# 重新構建
docker build --no-cache -t sotime-app:latest .

# 啟動服務
docker-compose up -d
```

## 📞 獲取幫助

如果遇到問題：

1. **查看日誌**: `docker-compose logs`
2. **檢查狀態**: `docker-compose ps`
3. **健康檢查**: `curl http://localhost:6666/health`
4. **重啟服務**: `docker-compose restart`
5. **查看詳細文檔**: [DEPLOYMENT.md](DEPLOYMENT.md)

---

**🎉 恭喜！您的 SoTime 應用現在運行在 http://localhost:6666**
