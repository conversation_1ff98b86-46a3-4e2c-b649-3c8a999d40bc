const { Storage } = require('@google-cloud/storage');
const path = require('path');
require('dotenv').config();

// 初始化 Google Cloud Storage
const keyFilePath = process.env.GCP_KEY_FILE
  ? (path.isAbsolute(process.env.GCP_KEY_FILE)
     ? process.env.GCP_KEY_FILE
     : path.join(__dirname, '..', process.env.GCP_KEY_FILE))
  : path.join(__dirname, 'ccw-dev-81e940b67f37.json');

const storage = new Storage({
  keyFilename: keyFilePath,
  projectId: process.env.GCP_PROJECT_ID || 'ccw-dev',
});

const bucketName = process.env.GCP_BUCKET_NAME || 'liveshowsotime';
const bucket = storage.bucket(bucketName);

/**
 * 上傳檔案到 GCP Storage
 * @param {Buffer} fileBuffer - 檔案緩衝區
 * @param {string} fileName - 檔案名稱
 * @param {string} mimeType - 檔案 MIME 類型
 * @returns {Promise<string>} - 返回檔案的公開 URL
 */
async function uploadToGCS(fileBuffer, fileName, mimeType) {
  try {
    const file = bucket.file(fileName);

    // 上傳檔案（不設置 public: true，因為 bucket 使用統一存儲桶級別訪問控制）
    await file.save(fileBuffer, {
      metadata: {
        contentType: mimeType,
      },
    });

    // 返回公開 URL（假設 bucket 已設為公開可讀）
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;
    return publicUrl;
  } catch (error) {
    console.error('上傳到 GCS 失敗:', error);
    throw error;
  }
}

/**
 * 生成 Signed URL 用於直接上傳
 * @param {string} fileName - 檔案名稱
 * @param {string} mimeType - 檔案 MIME 類型
 * @param {number} expiresInMinutes - 過期時間（分鐘）
 * @returns {Promise<{signedUrl: string, publicUrl: string}>} - 返回簽名 URL 和公開 URL
 */
async function generateSignedUploadUrl(fileName, mimeType, expiresInMinutes = 15) {
  try {
    const file = bucket.file(fileName);

    const options = {
      version: 'v4',
      action: 'write',
      expires: Date.now() + expiresInMinutes * 60 * 1000,
      contentType: mimeType,
    };

    const [signedUrl] = await file.getSignedUrl(options);
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;

    console.log('生成 Signed URL:', {
      fileName,
      mimeType,
      signedUrl: signedUrl.substring(0, 100) + '...',
      publicUrl
    });

    return {
      signedUrl,
      publicUrl,
      fileName
    };
  } catch (error) {
    console.error('生成 Signed URL 失敗:', error);
    throw error;
  }
}

/**
 * 驗證檔案是否已成功上傳到 GCS
 * @param {string} fileName - 檔案名稱
 * @returns {Promise<boolean>} - 檔案是否存在
 */
async function verifyFileExists(fileName) {
  try {
    const file = bucket.file(fileName);
    const [exists] = await file.exists();
    return exists;
  } catch (error) {
    console.error('驗證檔案存在失敗:', error);
    return false;
  }
}

/**
 * 從 GCP Storage 刪除檔案
 * @param {string} fileName - 檔案名稱
 * @returns {Promise<void>}
 */
async function deleteFromGCS(fileName) {
  try {
    const file = bucket.file(fileName);
    await file.delete();
    console.log(`檔案 ${fileName} 已從 GCS 刪除`);
  } catch (error) {
    console.error('從 GCS 刪除檔案失敗:', error);
    throw error;
  }
}

/**
 * 從 URL 中提取檔案名稱
 * @param {string} url - GCS URL
 * @returns {string} - 檔案名稱
 */
function getFileNameFromUrl(url) {
  if (!url || typeof url !== 'string') return '';
  const urlParts = url.split('/');
  return urlParts[urlParts.length - 1];
}

/**
 * 檢查檔案是否存在於 GCS
 * @param {string} fileName - 檔案名稱
 * @returns {Promise<boolean>} - 檔案是否存在
 */
async function fileExists(fileName) {
  try {
    const file = bucket.file(fileName);
    const [exists] = await file.exists();
    return exists;
  } catch (error) {
    console.error('檢查檔案是否存在失敗:', error);
    return false;
  }
}

/**
 * 獲取檔案的元數據
 * @param {string} fileName - 檔案名稱
 * @returns {Promise<object>} - 檔案元數據
 */
async function getFileMetadata(fileName) {
  try {
    const file = bucket.file(fileName);
    const [metadata] = await file.getMetadata();
    return metadata;
  } catch (error) {
    console.error('獲取檔案元數據失敗:', error);
    throw error;
  }
}

module.exports = {
  uploadToGCS,
  deleteFromGCS,
  getFileNameFromUrl,
  fileExists,
  getFileMetadata,
  generateSignedUploadUrl,
  verifyFileExists,
  bucket,
  storage
};
