# SoTime - 社群牆 + 直播平台

一個現代化的社交媒體平台，結合貼文牆和直播功能，使用 Vue.js 3 + Node.js + MongoDB 構建。

## ✨ 功能特色

* 🔐 JWT 使用者註冊 / 登入
* 📝 上傳文字 / 圖片 / 影片貼文
* 👍 貼文點讚、評論系統
* 📺 直播間功能
* 🏆 排行榜系統
* 📱 響應式設計，支持移動端

## 🚀 快速開始 (Docker 部署)

### 前置需求
- Docker 20.10+
- Docker Compose 2.0+

### 一鍵部署
```bash
# 克隆項目
git clone <your-repo-url>
cd WallServer

# 設置執行權限
chmod +x deploy.sh

# 啟動服務
./deploy.sh prod start
```

🎉 **訪問地址：**
- 本地開發：http://localhost:6666
- 正式環境：https://liveshow.sotime.app

### 其他操作
```bash
# 查看狀態
./deploy.sh prod status

# 查看日誌
./deploy.sh prod logs

# 重啟服務
./deploy.sh prod restart

# 停止服務
./deploy.sh prod stop
```

## 🛠️ 開發環境

### 傳統方式
```bash
# 安裝依賴
npm install
cd client && npm install

# 啟動開發服務器
npm run dev:all
```

- 前端: http://localhost:5173
- 後端: http://localhost:5003

### Docker 開發
```bash
# 構建開發環境
docker-compose -f docker-compose.dev.yml up
```

## 🏗️ 技術架構

### 前端
- **Vue.js 3.5** - 使用 Composition API
- **Vite 6.3** - 現代化構建工具
- **Tailwind CSS 4.1** - 原子化 CSS 框架
- **Video.js 8.22** - 視頻播放器
- **Axios** - HTTP 客戶端

### 後端
- **Node.js + Express.js** - Web 應用框架
- **MongoDB + Mongoose** - 數據庫和 ODM
- **JWT** - 身份驗證
- **Multer** - 文件上傳處理

### 部署
- **Docker + Docker Compose** - 容器化部署
- **Nginx** - 反向代理和靜態文件服務
- **GitHub Actions** - CI/CD 自動部署

## 📡 API 端點

| Method | Path | 說明 |
| --- | --- | --- |
| POST | /api/auth/register | 用戶註冊 |
| POST | /api/auth/login | 用戶登入 |
| GET | /api/auth/me | 獲取當前用戶信息 |
| GET | /api/posts | 獲取貼文列表 |
| POST | /api/posts | 創建貼文 |
| PUT | /api/posts/:id | 編輯貼文 |
| DELETE | /api/posts/:id | 刪除貼文 |
| POST | /api/posts/:id/like | 點讚/取消點讚 |
| POST | /api/posts/:id/comments | 添加評論 |
| GET | /api/live-rooms | 獲取直播間列表 |
| GET | /api/categories | 獲取分類列表 |
| GET | /api/rankings/:type | 獲取排行榜 |

## 🔧 環境配置

### 環境變量
```bash
# 應用配置
NODE_ENV=production
PORT=5003
JWT_SECRET=your_jwt_secret

# 數據庫配置
MONGO_URI=********************************:port/database
```

### Docker 端口映射
- **應用**: 6666 (統一訪問端口)
- **MongoDB**: 27017
- **Redis**: 6379

## 📚 詳細文檔

- [部署指南](DEPLOYMENT.md) - 完整的 Docker 部署文檔
- [API 文檔](docs/API.md) - 詳細的 API 接口說明
- [開發指南](docs/DEVELOPMENT.md) - 開發環境設置和貢獻指南

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📄 許可證

MIT License