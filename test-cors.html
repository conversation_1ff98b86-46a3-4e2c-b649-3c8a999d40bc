<!DOCTYPE html>
<html>
<head>
    <title>CORS 測試</title>
</head>
<body>
    <h1>GCS CORS 測試</h1>
    <button onclick="testCORS()">測試 CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '測試中...';

            try {
                // 測試 OPTIONS 請求（預檢請求）
                const response = await fetch('https://storage.googleapis.com/liveshowsotime/test-cors', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'https://liveshow.sotime.app',
                        'Access-Control-Request-Method': 'PUT',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                console.log('CORS 測試回應:', response);
                console.log('回應標頭:', [...response.headers.entries()]);

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>✅ CORS 測試成功！</h3>
                        <p>狀態碼: ${response.status}</p>
                        <p>Access-Control-Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin')}</p>
                        <p>Access-Control-Allow-Methods: ${response.headers.get('Access-Control-Allow-Methods')}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ CORS 測試失敗</h3>
                        <p>狀態碼: ${response.status}</p>
                        <p>錯誤: ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                console.error('CORS 測試錯誤:', error);
                resultDiv.innerHTML = `
                    <h3>❌ CORS 測試錯誤</h3>
                    <p>錯誤: ${error.message}</p>
                `;
            }
        }

        // 自動執行測試
        window.onload = () => {
            setTimeout(testCORS, 1000);
        };
    </script>
</body>
</html>
