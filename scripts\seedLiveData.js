const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Banner = require('../models/Banner');
const Category = require('../models/Category');
const LiveRoom = require('../models/LiveRoom');
const User = require('../models/User');
const Ranking = require('../models/Ranking');
const faker = require('@faker-js/faker').faker;

dotenv.config();

async function seed() {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/poster');

    console.log('MongoDB connected');

    // 清空資料
    await Banner.deleteMany();
    await Category.deleteMany();
    await LiveRoom.deleteMany();
    await Ranking.deleteMany();

    // 建立分類
    const categoriesData = [
      { name: '遊戲', slug: 'game', order: 1 },
      { name: '聊天', slug: 'chat', order: 2 },
      { name: '音樂', slug: 'music', order: 3 },
      { name: '美食', slug: 'food', order: 4 },
      { name: '戶外', slug: 'outdoor', order: 5 },
      { name: '舞蹈', slug: 'dance', order: 6 },
      { name: '藝術', slug: 'art', order: 7 },
      { name: '運動', slug: 'sports', order: 8 },
    ];

    const categories = await Category.insertMany(categoriesData);

    // 建立 Banner
    const bannersData = [
      {
        title: '2025 夏季電競大賽現正直播中',
        image: 'https://readdy.ai/api/search-image?query=Esports%20tournament%20arena%20with%20professional%20gamers%20competing%20on%20stage%2C%20dramatic%20lighting%2C%20large%20screens%20showing%20gameplay%2C%20enthusiastic%20audience%20in%20the%20background%2C%20high-quality%20photography%20style%2C%20vibrant%20atmosphere%2C%20cinematic%20composition%2C%20high%20detail%2C%20realistic&width=800&height=400&seq=1&orientation=landscape',
        order: 1,
      },
      {
        title: '人氣主播「小雪」音樂週末派對',
        image: 'https://readdy.ai/api/search-image?query=Female%20streamer%20performing%20music%20in%20a%20cozy%20studio%20setting%20with%20soft%20lighting%2C%20musical%20instruments%20visible%2C%20professional%20streaming%20setup%20with%20microphones%20and%20audio%20equipment%2C%20warm%20atmosphere%2C%20high-quality%20photography%2C%20cinematic%20composition&width=800&height=400&seq=2&orientation=landscape',
        order: 2,
      },
      {
        title: '美食直播：台灣夜市小吃特輯',
        image: 'https://readdy.ai/api/search-image?query=Vibrant%20Taiwanese%20night%20market%20food%20stalls%20with%20colorful%20street%20food%2C%20steaming%20dishes%2C%20neon%20lights%2C%20bustling%20atmosphere%2C%20food%20vendors%20cooking%2C%20high-quality%20food%20photography%2C%20atmospheric%20lighting%2C%20cinematic%20composition%2C%20realistic%20details&width=800&height=400&seq=3&orientation=landscape',
        order: 3,
      },
    ];
    await Banner.insertMany(bannersData);

    // 取幾位用戶做為主播 (需要先在資料庫有這些用戶)
    let hosts = await User.find().limit(5);
    if (hosts.length === 0) {
      console.log('未偵測到 User，開始自動建立假用戶');
      const hostData = Array.from({ length: 7 }).map(() => ({
        name: faker.person.firstName(),
        username: faker.internet.userName(),
        email: faker.internet.email(),
        password: '123456',
        avatar: faker.image.avatar(),
      }));
      hosts = await User.insertMany(hostData);
    }

    const catMap = categories.reduce((obj, cat) => {
      obj[cat.slug] = cat._id;
      return obj;
    }, {});

    const liveRoomsData = [
      {
        title: '【英雄聯盟】衝刺鑽石排位賽',
        host: hosts[0]._id,
        cover: 'https://readdy.ai/api/search-image?query=League%20of%20Legends%20gameplay%20screenshot&width=400&height=225&seq=11&orientation=landscape',
        viewers: 12583,
        category: catMap['game'],
        tags: ['英雄聯盟'],
      },
      {
        title: '週末音樂放鬆時光 ♪',
        host: hosts[1]._id,
        cover: 'https://readdy.ai/api/search-image?query=Female%20streamer%20playing%20acoustic%20guitar&width=400&height=225&seq=12&orientation=landscape',
        viewers: 8742,
        category: catMap['music'],
        tags: ['吉他'],
      },
    ];

    await LiveRoom.insertMany(liveRoomsData);

    // 額外自動產生隨機直播間 20 筆
    const extraRooms = Array.from({ length: 20 }).map((_v, idx) => {
      const host = hosts[idx % hosts.length];
      const catKeys = Object.keys(catMap).filter(k => k !== 'all');
      const slug = catKeys[idx % catKeys.length];
      return {
        title: faker.lorem.words({ min: 3, max: 6 }),
        host: host._id,
        cover: faker.image.urlLoremFlickr({ category: 'nature' }),
        viewers: faker.number.int({ min: 100, max: 10000 }),
        category: catMap[slug],
        tags: [faker.word.noun()],
      };
    });
    await LiveRoom.insertMany(extraRooms);

    const audienceDay = {
      type: 'audience',
      period: 'day',
      list: [
        { name: '富豪張先生', avatar: 'https://readdy.ai/api/search-image?query=Wealthy...&width=200&height=200', badge: 'VIP', contribution: '支持了 23 位主播', giftValue: 258000 },
        { name: '科技宅男', avatar: 'https://readdy.ai/api/search-image?query=Young%20Asian%20tech...&width=200&height=200', badge: '黃金會員', contribution: '支持了 5 位主播', giftValue: 156000 },
      ],
    };
    const streamerDay = {
      type: 'streamer',
      period: 'day',
      list: [
        { name: '小雪', avatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20streamer...', badge: '人氣主播', contribution: '收到 1253 份禮物', giftValue: 320000 },
        { name: '阿翔', avatar: 'https://readdy.ai/api/search-image?query=Asian%20male%20streamer...', badge: '遊戲達人', contribution: '收到 986 份禮物', giftValue: 245000 },
      ],
    };
    await Ranking.insertMany([audienceDay, streamerDay]);

    console.log('Seed 完成');
    process.exit(0);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
}

seed(); 