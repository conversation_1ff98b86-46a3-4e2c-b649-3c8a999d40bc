<!DOCTYPE html>
<html>
<head>
    <title>直接上傳測試</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>GCS 直接上傳測試</h1>
    
    <div>
        <input type="file" id="fileInput" accept="image/*,video/*" multiple>
        <button onclick="testDirectUpload()">測試直接上傳</button>
    </div>
    
    <div id="progress" style="margin-top: 20px;"></div>
    <div id="result" style="margin-top: 20px;"></div>

    <script>
        // 設定 axios 基礎 URL
        axios.defaults.baseURL = 'https://liveshow.sotime.app/api';
        
        // 添加認證 token（需要替換為實際的 token）
        const token = localStorage.getItem('token') || prompt('請輸入認證 token:');
        if (token) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        }

        async function testDirectUpload() {
            const fileInput = document.getElementById('fileInput');
            const progressDiv = document.getElementById('progress');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('請選擇檔案');
                return;
            }

            const files = Array.from(fileInput.files);
            progressDiv.innerHTML = '開始測試...';
            resultDiv.innerHTML = '';

            try {
                // 1. 準備檔案資訊
                const fileInfos = files.map(file => ({
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    fileType: file.type.startsWith('video/') ? 'video' : 'image'
                }));

                progressDiv.innerHTML = '獲取上傳憑證...';

                // 2. 獲取上傳憑證
                const credentialsResponse = await axios.post('/upload/signed-url', {
                    files: fileInfos
                });

                const credentials = credentialsResponse.data.credentials;
                progressDiv.innerHTML = `獲取到 ${credentials.length} 個上傳憑證`;

                // 3. 逐個上傳檔案
                const results = [];
                for (let i = 0; i < credentials.length; i++) {
                    const file = files[i];
                    const credential = credentials[i];

                    progressDiv.innerHTML = `上傳檔案 ${i + 1}/${credentials.length}: ${file.name}`;

                    try {
                        // 直接上傳到 GCS
                        await axios.put(credential.signedUrl, file, {
                            headers: {
                                'Content-Type': credential.mimeType,
                            },
                            onUploadProgress: (progressEvent) => {
                                if (progressEvent.total) {
                                    const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                                    progressDiv.innerHTML = `上傳檔案 ${i + 1}/${credentials.length}: ${file.name} (${percentage}%)`;
                                }
                            },
                            timeout: 300000,
                            transformRequest: [(data) => data],
                        });

                        results.push({
                            fileName: credential.fileName,
                            publicUrl: credential.publicUrl,
                            originalName: credential.originalName,
                            mimeType: credential.mimeType,
                            fileType: credential.fileType
                        });

                        console.log(`✅ 檔案 ${file.name} 上傳成功`);
                    } catch (uploadError) {
                        console.error(`❌ 檔案 ${file.name} 上傳失敗:`, uploadError);
                        throw uploadError;
                    }
                }

                progressDiv.innerHTML = '驗證上傳結果...';

                // 4. 驗證上傳完成
                const verifyResponse = await axios.post('/upload/verify', {
                    files: results
                });

                progressDiv.innerHTML = '✅ 測試完成！';
                resultDiv.innerHTML = `
                    <h3>上傳成功！</h3>
                    <p>成功上傳 ${results.length} 個檔案</p>
                    <ul>
                        ${results.map(result => `
                            <li>
                                <strong>${result.originalName}</strong><br>
                                <a href="${result.publicUrl}" target="_blank">${result.publicUrl}</a>
                            </li>
                        `).join('')}
                    </ul>
                `;

            } catch (error) {
                console.error('測試失敗:', error);
                progressDiv.innerHTML = '❌ 測試失敗';
                resultDiv.innerHTML = `
                    <h3>錯誤詳情</h3>
                    <p><strong>錯誤訊息:</strong> ${error.message}</p>
                    <p><strong>狀態碼:</strong> ${error.response?.status}</p>
                    <p><strong>回應:</strong> ${JSON.stringify(error.response?.data, null, 2)}</p>
                `;
            }
        }

        // 頁面載入時檢查認證
        window.onload = () => {
            if (!token) {
                document.body.innerHTML = '<h1>需要認證 token 才能測試</h1>';
            }
        };
    </script>
</body>
</html>
