/**
 * 影片壓縮工具
 * 使用 Canvas API 和 MediaRecorder API 進行客戶端影片壓縮
 */

export interface VideoCompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0.1 - 1.0
  maxSizeKB?: number; // 最大檔案大小（KB）
  maxDuration?: number; // 最大時長（秒）
  videoBitrate?: number; // 影片位元率 (bps)
  audioBitrate?: number; // 音訊位元率 (bps)
}

export interface VideoCompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  duration: number;
  width: number;
  height: number;
}

/**
 * 壓縮影片檔案
 * @param file 原始影片檔案
 * @param options 壓縮選項
 * @param onProgress 進度回調
 * @returns 壓縮結果
 */
export async function compressVideo(
  file: File,
  options: VideoCompressionOptions = {},
  onProgress?: (progress: number) => void
): Promise<VideoCompressionResult> {
  const {
    maxWidth = 1280,
    maxHeight = 720,
    quality = 0.7,
    maxSizeKB = 10240, // 10MB
    maxDuration = 300, // 5分鐘
    videoBitrate = 1000000, // 1Mbps
    audioBitrate = 128000 // 128kbps
  } = options;

  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('無法創建 Canvas 上下文'));
      return;
    }

    video.onloadedmetadata = async () => {
      try {
        // 檢查影片時長
        if (video.duration > maxDuration) {
          reject(new Error(`影片時長超過限制 (${maxDuration}秒)`));
          return;
        }

        // 計算新的尺寸
        const { width, height } = calculateNewDimensions(
          video.videoWidth,
          video.videoHeight,
          maxWidth,
          maxHeight
        );

        canvas.width = width;
        canvas.height = height;

        // 檢查瀏覽器是否支援 MediaRecorder
        if (!MediaRecorder.isTypeSupported('video/webm;codecs=vp8')) {
          // 如果不支援壓縮，返回原始檔案
          console.warn('瀏覽器不支援影片壓縮，使用原始檔案');
          resolve({
            file,
            originalSize: file.size,
            compressedSize: file.size,
            compressionRatio: 0,
            duration: video.duration,
            width: video.videoWidth,
            height: video.videoHeight
          });
          return;
        }

        // 創建 MediaStream
        const stream = canvas.captureStream(30); // 30 FPS
        
        // 添加音訊軌道（如果存在）
        try {
          const audioContext = new AudioContext();
          const source = audioContext.createMediaElementSource(video);
          const destination = audioContext.createMediaStreamDestination();
          source.connect(destination);
          
          if (destination.stream.getAudioTracks().length > 0) {
            stream.addTrack(destination.stream.getAudioTracks()[0]);
          }
        } catch (audioError) {
          console.warn('無法處理音訊:', audioError);
        }

        // 獲取最佳支援的影片格式
        const bestFormat = getBestSupportedVideoFormat();

        const mediaRecorder = new MediaRecorder(stream, {
          mimeType: bestFormat.mimeType,
          videoBitsPerSecond: videoBitrate,
          audioBitsPerSecond: audioBitrate
        });

        const chunks: Blob[] = [];
        let startTime = Date.now();

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const compressedBlob = new Blob(chunks, { type: bestFormat.mimeType });

          // 創建新的 File 對象
          const compressedFile = new File(
            [compressedBlob],
            file.name.replace(/\.[^/.]+$/, bestFormat.extension),
            {
              type: bestFormat.type,
              lastModified: Date.now()
            }
          );

          const result: VideoCompressionResult = {
            file: compressedFile,
            originalSize: file.size,
            compressedSize: compressedBlob.size,
            compressionRatio: Math.round((1 - compressedBlob.size / file.size) * 100),
            duration: video.duration,
            width,
            height
          };

          resolve(result);
        };

        mediaRecorder.onerror = (event) => {
          reject(new Error('影片壓縮失敗'));
        };

        // 開始錄製
        mediaRecorder.start(100); // 每100ms收集一次數據

        // 播放影片並繪製到 Canvas
        video.currentTime = 0;
        video.play();

        const drawFrame = () => {
          if (video.ended || video.paused) {
            mediaRecorder.stop();
            return;
          }

          ctx.drawImage(video, 0, 0, width, height);
          
          // 更新進度
          if (onProgress) {
            const progress = Math.round((video.currentTime / video.duration) * 100);
            onProgress(progress);
          }

          requestAnimationFrame(drawFrame);
        };

        video.onplay = () => {
          drawFrame();
        };

      } catch (error) {
        reject(error);
      }
    };

    video.onerror = () => {
      reject(new Error('影片載入失敗'));
    };

    // 載入影片
    video.src = URL.createObjectURL(file);
    video.load();
  });
}

/**
 * 計算新的影片尺寸（保持比例）
 */
function calculateNewDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let { width, height } = { width: originalWidth, height: originalHeight };

  // 如果影片尺寸小於限制，不需要縮放
  if (width <= maxWidth && height <= maxHeight) {
    return { width, height };
  }

  // 計算縮放比例
  const widthRatio = maxWidth / width;
  const heightRatio = maxHeight / height;
  const ratio = Math.min(widthRatio, heightRatio);

  // 確保尺寸是偶數（某些編碼器要求）
  return {
    width: Math.round(width * ratio / 2) * 2,
    height: Math.round(height * ratio / 2) * 2
  };
}

/**
 * 獲取影片資訊
 */
export async function getVideoInfo(file: File): Promise<{
  duration: number;
  width: number;
  height: number;
  size: number;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    
    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        size: file.size
      });
    };

    video.onerror = () => {
      reject(new Error('無法讀取影片資訊'));
    };

    video.src = URL.createObjectURL(file);
    video.load();
  });
}

/**
 * 檢查瀏覽器是否支援影片壓縮
 */
export function isVideoCompressionSupported(): boolean {
  return typeof MediaRecorder !== 'undefined' &&
         typeof HTMLCanvasElement !== 'undefined' &&
         typeof HTMLVideoElement !== 'undefined';
}

/**
 * 獲取瀏覽器支援的最佳影片格式
 */
export function getBestSupportedVideoFormat(): { mimeType: string; extension: string; type: string } {
  // 優先順序：MP4 (H.264) > WebM (VP8) > WebM (基本)
  const formats = [
    { mimeType: 'video/mp4;codecs=avc1.42E01E,mp4a.40.2', extension: '.mp4', type: 'video/mp4' },
    { mimeType: 'video/webm;codecs=vp8,opus', extension: '.webm', type: 'video/webm' },
    { mimeType: 'video/webm', extension: '.webm', type: 'video/webm' }
  ];

  for (const format of formats) {
    if (MediaRecorder.isTypeSupported(format.mimeType)) {
      return format;
    }
  }

  // 如果都不支援，回退到基本格式
  return { mimeType: 'video/webm', extension: '.webm', type: 'video/webm' };
}

/**
 * 格式化時長
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}
