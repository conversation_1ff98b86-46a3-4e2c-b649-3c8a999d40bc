const mongoose = require('mongoose');

const liveRoomSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    host: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    cover: { type: String, required: true },
    viewers: { type: Number, default: 0 },
    category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
    tags: [{ type: String }],
    isLive: { type: Boolean, default: true },
    streamUrl: { type: String },
  },
  { timestamps: true }
);

module.exports = mongoose.model('LiveRoom', liveRoomSchema); 