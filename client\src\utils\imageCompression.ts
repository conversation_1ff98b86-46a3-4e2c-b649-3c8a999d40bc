/**
 * 圖片壓縮工具
 * 使用 Canvas API 進行客戶端圖片壓縮
 */

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0.1 - 1.0
  format?: 'jpeg' | 'png' | 'webp';
  maxSizeKB?: number; // 最大檔案大小（KB）
}

export interface CompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}

/**
 * 壓縮圖片檔案
 * @param file 原始圖片檔案
 * @param options 壓縮選項
 * @returns 壓縮結果
 */
export async function compressImage(
  file: File,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8,
    format = 'jpeg',
    maxSizeKB = 1024 // 1MB
  } = options;

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      try {
        // 計算新的尺寸
        const { width, height } = calculateNewDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight
        );

        // 創建 Canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('無法創建 Canvas 上下文'));
          return;
        }

        canvas.width = width;
        canvas.height = height;

        // 繪製圖片到 Canvas
        ctx.drawImage(img, 0, 0, width, height);

        // 轉換為 Blob
        canvas.toBlob(
          async (blob) => {
            if (!blob) {
              reject(new Error('圖片壓縮失敗'));
              return;
            }

            let finalBlob = blob;
            let currentQuality = quality;

            // 如果檔案仍然太大，進一步降低品質
            while (finalBlob.size > maxSizeKB * 1024 && currentQuality > 0.1) {
              currentQuality -= 0.1;
              finalBlob = await new Promise<Blob>((resolve) => {
                canvas.toBlob(resolve, `image/${format}`, currentQuality);
              }) as Blob;
            }

            // 創建新的 File 對象
            const compressedFile = new File(
              [finalBlob],
              file.name.replace(/\.[^/.]+$/, `.${format === 'jpeg' ? 'jpg' : format}`),
              {
                type: `image/${format}`,
                lastModified: Date.now()
              }
            );

            const result: CompressionResult = {
              file: compressedFile,
              originalSize: file.size,
              compressedSize: finalBlob.size,
              compressionRatio: Math.round((1 - finalBlob.size / file.size) * 100)
            };

            resolve(result);
          },
          `image/${format}`,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('圖片載入失敗'));
    };

    // 載入圖片
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 計算新的圖片尺寸（保持比例）
 */
function calculateNewDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let { width, height } = { width: originalWidth, height: originalHeight };

  // 如果圖片尺寸小於限制，不需要縮放
  if (width <= maxWidth && height <= maxHeight) {
    return { width, height };
  }

  // 計算縮放比例
  const widthRatio = maxWidth / width;
  const heightRatio = maxHeight / height;
  const ratio = Math.min(widthRatio, heightRatio);

  return {
    width: Math.round(width * ratio),
    height: Math.round(height * ratio)
  };
}

/**
 * 批量壓縮圖片
 */
export async function compressImages(
  files: File[],
  options: CompressionOptions = {},
  onProgress?: (progress: number, currentFile: string) => void
): Promise<CompressionResult[]> {
  const results: CompressionResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    if (onProgress) {
      onProgress(Math.round((i / files.length) * 100), file.name);
    }
    
    try {
      const result = await compressImage(file, options);
      results.push(result);
    } catch (error) {
      console.error(`壓縮圖片 ${file.name} 失敗:`, error);
      // 如果壓縮失敗，使用原始檔案
      results.push({
        file,
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: 0
      });
    }
  }
  
  if (onProgress) {
    onProgress(100, '完成');
  }
  
  return results;
}

/**
 * 格式化檔案大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
