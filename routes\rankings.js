const express = require('express');
const router = express.Router();
const Ranking = require('../models/Ranking');

// 取得排行榜
// GET /api/rankings/:type?period=day
router.get('/:type', async (req, res) => {
  const { type } = req.params; // audience | streamer
  const { period = 'day' } = req.query;

  if (!['audience', 'streamer'].includes(type)) {
    return res.status(400).json({ message: '無效排名類型' });
  }

  try {
    const doc = await Ranking.findOne({ type, period });
    res.json(doc ? doc.list : []);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得排行榜失敗' });
  }
});

module.exports = router; 