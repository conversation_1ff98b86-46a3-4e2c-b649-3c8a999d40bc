<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
<div class="app-container bg-gray-900 min-h-screen text-gray-200">
<!-- 頂部導航欄 -->
<header class="fixed top-0 left-0 right-0 z-50 bg-gray-800 bg-opacity-95 shadow-md">
<div class="flex justify-between items-center px-4 h-14">
<a href="#" class="flex items-center text-gray-300 cursor-pointer">
<i class="fas fa-arrow-left text-lg"></i>
</a>
<div class="text-xl font-medium text-gray-100">個人中心</div>
<div class="relative">
<button
@click="showMoreMenu = !showMoreMenu"
class="w-8 h-8 flex items-center justify-center text-gray-300 cursor-pointer hover:text-gray-100"
>
<i class="fas fa-ellipsis-h"></i>
</button>
<!-- Dropdown Menu -->
<div
v-if="showMoreMenu"
class="absolute right-0 top-10 w-48 bg-gray-800 rounded-lg shadow-lg py-1 z-50 border border-gray-700"
>
<button
v-for="item in menuItems"
:key="item.id"
@click="handleMenuAction(item.action)"
class="w-full px-4 py-2.5 flex items-center text-gray-300 hover:bg-gray-700 hover:text-gray-100"
>
<i :class="['mr-3 text-gray-400', item.icon]"></i>
<span class="text-sm">{{ item.label }}</span>
</button>
</div>
<!-- Backdrop for closing menu -->
<div
v-if="showMoreMenu"
class="fixed inset-0 z-40"
@click="showMoreMenu = false"
></div>
</div>
</div>
</header>
<!-- 主內容區域 -->
<main class="pt-14 pb-16">
<!-- 個人資料卡片 -->
<div class="profile-card bg-gray-800 p-5 shadow-md">
<div class="flex items-start">
<div class="relative mr-4">
<div class="w-20 h-20 rounded-full overflow-hidden border-2 border-blue-500">
<img :src="userProfile.avatar" alt="用戶頭像" class="w-full h-full object-cover" />
</div>
<div class="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white cursor-pointer !rounded-button" @click="showAvatarModal = true">
<i class="fas fa-camera text-sm"></i>
</div>
<!-- Avatar Upload Modal -->
<div v-if="showAvatarModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 rounded-lg w-11/12 max-w-sm">
<div class="p-4 border-b border-gray-700">
<h3 class="text-lg font-medium text-gray-100">更換頭像</h3>
</div>
<div class="p-4 space-y-4">
<button @click="handleAvatarUpload('album')" class="w-full py-3 px-4 bg-gray-700 text-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-images mr-2"></i>
從相冊選擇
</button>
<button @click="handleAvatarUpload('camera')" class="w-full py-3 px-4 bg-gray-700 text-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-camera mr-2"></i>
拍攝照片
</button>
</div>
<div class="p-4 border-t border-gray-700">
<button @click="showAvatarModal = false" class="w-full py-2 text-gray-400 hover:text-gray-300 !rounded-button">
取消
</button>
</div>
</div>
</div>
<!-- Avatar Crop Modal -->
<div v-if="showAvatarCropModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 rounded-lg w-11/12 max-w-md">
<div class="p-4 border-b border-gray-700">
<h3 class="text-lg font-medium text-gray-100">調整頭像</h3>
</div>
<div class="p-4">
<div class="aspect-square w-full bg-gray-900 rounded-lg mb-4 flex items-center justify-center">
<img v-if="tempAvatarUrl" :src="tempAvatarUrl" alt="頭像預覽" class="max-w-full max-h-full object-contain" />
<div v-else class="text-gray-500">
<i class="fas fa-image text-4xl"></i>
</div>
</div>
</div>
<div class="p-4 border-t border-gray-700 flex justify-end space-x-3">
<button @click="cancelAvatarCrop" class="px-4 py-2 text-gray-400 hover:text-gray-300 !rounded-button">
取消
</button>
<button @click="confirmAvatarUpload" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 !rounded-button">
確認
</button>
</div>
</div>
</div>
<!-- Success Toast -->
<div v-if="showAvatarSuccessToast" class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
頭像更新成功
</div>
</div>
<div class="flex-1">
<div class="flex justify-between items-start">
<div>
<h2 class="text-xl font-bold text-gray-100">{{ userProfile.name }}</h2>
<p class="text-gray-400 text-sm mt-1">@{{ userProfile.username }}</p>
</div>
<button @click="showEditModal = true" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors cursor-pointer">
<i class="fas fa-edit text-lg"></i>
</button>
<!-- 編輯個人資料模態框 -->
<div v-if="showEditModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 rounded-lg w-11/12 max-w-md">
<div class="p-4 border-b border-gray-700">
<h3 class="text-lg font-medium text-gray-100">編輯個人資料</h3>
</div>
<div class="p-4">
<div class="space-y-4">
<div>
<label class="block text-sm text-gray-400 mb-1">姓名</label>
<input
type="text"
v-model="editForm.name"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
>
</div>
<div>
<label class="block text-sm text-gray-400 mb-1">用戶名</label>
<input
type="text"
v-model="editForm.username"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
>
</div>
<div class="grid grid-cols-2 gap-4">
<div>
<label class="block text-sm text-gray-400 mb-1">生日</label>
<input
type="date"
v-model="editForm.birthday"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
>
</div>
<div>
<label class="block text-sm text-gray-400 mb-1">性別</label>
<div class="relative">
<select
v-model="editForm.gender"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none appearance-none cursor-pointer"
>
<option value="">請選擇</option>
<option value="male">男性</option>
<option value="female">女性</option>
<option value="other">其他</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
<i class="fas fa-chevron-down text-gray-400"></i>
</div>
</div>
</div>
</div>
<div>
<label class="block text-sm text-gray-400 mb-1">個人簡介</label>
<textarea
v-model="editForm.bio"
rows="3"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
></textarea>
</div>
<div>
<label class="block text-sm text-gray-400 mb-1">地點</label>
<input
type="text"
v-model="editForm.location"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
>
</div>
</div>
</div>
<div class="p-4 border-t border-gray-700 flex justify-end space-x-3">
<button
@click="showEditModal = false"
class="px-4 py-2 text-gray-400 hover:text-gray-300 !rounded-button"
>
取消
</button>
<button
@click="saveProfile"
class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 !rounded-button"
>
保存
</button>
</div>
</div>
</div>
<!-- 成功提示 -->
<div
v-if="showSuccessToast"
class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50"
>
個人資料更新成功
</div>
</div>
<p class="text-gray-300 mt-3 text-sm leading-relaxed">{{ userProfile.bio }}</p>
<div class="flex items-center mt-2 text-sm text-gray-400">
<div class="flex items-center mr-4">
<i class="fas fa-map-marker-alt mr-1 text-gray-500"></i>
<span>{{ userProfile.location }}</span>
</div>
<div class="flex items-center">
<i class="fas fa-calendar-alt mr-1 text-gray-500"></i>
<span>{{ userProfile.joinDate }}</span>
</div>
</div>
</div>
</div>
</div>
<!-- 數據統計區 -->
<div class="stats-card bg-gray-800 mt-3 grid grid-cols-4 py-4 shadow-md">
<div v-for="(stat, index) in userStats" :key="index" class="stat-item flex flex-col items-center">
<div class="text-lg font-bold text-gray-100">{{ stat.value }}</div>
<div class="text-xs text-gray-400 mt-1">{{ stat.label }}</div>
</div>
</div>
<!-- 帳戶資產區 -->
<div class="wallet-section bg-gray-800 mt-3 p-4 shadow-md">
<div class="flex justify-between items-center mb-4">
<div class="flex items-center">
<i class="fas fa-wallet text-blue-400 text-xl mr-2"></i>
<span class="text-gray-100 font-medium">我的錢包</span>
</div>
<button @click="showWalletDetails = !showWalletDetails" class="text-gray-400">
<i :class="['fas', showWalletDetails ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
</button>
</div>
<div v-if="showWalletDetails" class="space-y-4">
<div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
<div>
<div class="text-gray-300 text-sm">當前餘額</div>
<div class="text-xl font-bold text-blue-400 mt-1">{{ walletInfo.balance }} 金幣</div>
</div>
<button @click="showRechargeModal = true" class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm hover:bg-blue-600 transition-colors !rounded-button">
充值
</button>
<!-- Recharge Modal -->
<div v-if="showRechargeModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-w-md rounded-lg overflow-hidden">
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">充值鑽石</h3>
<button @click="showRechargeModal = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<div class="p-4">
<!-- Amount Selection -->
<div class="mb-4">
<label class="block text-sm text-gray-400 mb-2">選擇充值金額</label>
<div class="grid grid-cols-2 gap-3">
<button
v-for="option in rechargeOptions"
:key="option.amount"
@click="selectedAmount = option.amount"
:class="[
'p-3 rounded-lg border text-left transition-colors !rounded-button',
selectedAmount === option.amount
? 'bg-blue-600 border-blue-500 text-white'
: 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
]"
>
<div class="text-lg font-medium">{{ option.amount }} 鑽石</div>
<div class="text-sm mt-1" :class="selectedAmount === option.amount ? 'text-blue-200' : 'text-gray-400'">
NT$ {{ option.price }}
</div>
<div v-if="option.bonus > 0" class="text-xs mt-1 text-green-400">
贈送 {{ option.bonus }} 鑽石
</div>
</button>
</div>
</div>
<!-- Payment Method Selection -->
<div>
<label class="block text-sm text-gray-400 mb-2">選擇支付方式</label>
<div class="space-y-2">
<button
v-for="method in paymentMethods"
:key="method.id"
@click="selectedPayment = method.id"
:class="[
'w-full p-3 rounded-lg flex items-center transition-colors !rounded-button',
selectedPayment === method.id
? 'bg-blue-600 text-white'
: 'bg-gray-700 text-gray-300 hover:bg-gray-600'
]"
>
<i :class="['fas', method.icon, 'text-xl']"></i>
<span class="ml-3">{{ method.name }}</span>
</button>
</div>
</div>
</div>
<div class="p-4 border-t border-gray-700">
<button
@click="handleRecharge"
:disabled="!selectedAmount || !selectedPayment"
:class="[
'w-full py-3 rounded-lg text-white font-medium transition-colors !rounded-button',
selectedAmount && selectedPayment
? 'bg-blue-600 hover:bg-blue-700'
: 'bg-gray-600 cursor-not-allowed'
]"
>
確認充值
</button>
</div>
</div>
</div>
<!-- Processing Modal -->
<div v-if="showPaymentProcessing" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 rounded-lg p-6 flex flex-col items-center">
<div class="w-16 h-16 mb-4">
<i class="fas fa-spinner fa-spin text-4xl text-blue-400"></i>
</div>
<p class="text-gray-200">支付處理中...</p>
</div>
</div>
<!-- Success Toast -->
<div v-if="showRechargeSuccess" class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center">
<i class="fas fa-check-circle mr-2"></i>
充值成功
</div>
</div>
<div class="grid grid-cols-2 gap-4">
<button @click="showWithdrawModal = true" class="flex items-center justify-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-money-bill-wave text-green-400 mr-2"></i>
<span class="text-gray-200">主播提現</span>
</button>
<!-- Withdrawal Modal -->
<div v-if="showWithdrawModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-w-md rounded-lg overflow-hidden">
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">主播提現</h3>
<button @click="showWithdrawModal = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<div class="p-4 space-y-4">
<!-- Available Balance -->
<div class="bg-gray-700 rounded-lg p-4">
<div class="text-sm text-gray-400">可提現餘額</div>
<div class="text-2xl font-bold text-blue-400 mt-1">
<i class="fas fa-gem text-sm mr-1"></i>
{{ walletInfo.withdrawable }} 鑽石
</div>
</div>
<!-- Withdrawal Amount -->
<div>
<label class="block text-sm text-gray-400 mb-1">提現金額</label>
<div class="relative">
<input
v-model="withdrawForm.amount"
type="number"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
placeholder="請輸入提現金額"
>
<span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">鑽石</span>
</div>
</div>
<!-- Withdrawal Method -->
<div>
<label class="block text-sm text-gray-400 mb-1">提現方式</label>
<div class="relative">
<select
v-model="withdrawForm.method"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none appearance-none cursor-pointer"
>
<option value="">請選擇提現方式</option>
<option value="bank">銀行卡</option>
<option value="alipay">支付寶</option>
<option value="wechat">微信支付</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
<i class="fas fa-chevron-down text-gray-400"></i>
</div>
</div>
</div>
<!-- Account Information -->
<div v-if="withdrawForm.method">
<label class="block text-sm text-gray-400 mb-1">收款賬戶信息</label>
<div class="space-y-3">
<input
v-model="withdrawForm.accountName"
type="text"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
placeholder="收款人姓名"
>
<input
v-model="withdrawForm.accountNumber"
type="text"
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
:placeholder="accountPlaceholder"
>
</div>
</div>
<!-- Withdrawal Notes -->
<div class="bg-gray-700 rounded-lg p-3">
<h4 class="text-sm font-medium text-gray-200 mb-2">提現說明</h4>
<ul class="space-y-1">
<li class="text-xs text-gray-400 flex items-start">
<i class="fas fa-info-circle mt-0.5 mr-1"></i>
提現手續費率為1%，最低2鑽石
</li>
<li class="text-xs text-gray-400 flex items-start">
<i class="fas fa-info-circle mt-0.5 mr-1"></i>
工作日16:00前提現，當天到賬；16:00後提現，次日到賬
</li>
<li class="text-xs text-gray-400 flex items-start">
<i class="fas fa-info-circle mt-0.5 mr-1"></i>
單筆提現最低100鑽石，最高50000鑽石
</li>
</ul>
</div>
</div>
<div class="p-4 border-t border-gray-700 flex justify-end space-x-3">
<button
@click="showWithdrawModal = false"
class="px-4 py-2 text-gray-400 hover:text-gray-300 !rounded-button"
>
取消
</button>
<button
@click="submitWithdrawal"
:disabled="!isWithdrawFormValid"
:class="[
'px-4 py-2 rounded-lg text-white transition-colors !rounded-button',
isWithdrawFormValid
? 'bg-blue-600 hover:bg-blue-700'
: 'bg-gray-600 cursor-not-allowed'
]"
>
確認提現
</button>
</div>
</div>
</div>
<!-- Processing Modal -->
<div v-if="showWithdrawProcessing" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 rounded-lg p-6 flex flex-col items-center">
<div class="w-16 h-16 mb-4">
<i class="fas fa-spinner fa-spin text-4xl text-blue-400"></i>
</div>
<p class="text-gray-200">提現申請處理中...</p>
</div>
</div>
<!-- Success Toast -->
<div v-if="showWithdrawSuccess" class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center">
<i class="fas fa-check-circle mr-2"></i>
提現申請已提交，請等待審核
</div>
<button @click="showStreamerApplyModal = true" class="flex items-center justify-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-star text-yellow-400 mr-2"></i>
<span class="text-gray-200">{{ isStreamer ? '主播等級' : '申請成為主播' }}</span>
</button>
<!-- Streamer Application Modal -->
<div v-if="showStreamerApplyModal && !isStreamer" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-w-md rounded-lg overflow-hidden">
<div class="p-4 border-b border-gray-700">
<h3 class="text-lg font-medium text-gray-100">申請成為主播</h3>
</div>
<div class="p-4">
<form @submit.prevent="submitStreamerApplication" class="space-y-4">
<!-- Stream Category -->
<div>
<label class="block text-sm text-gray-400 mb-1">直播類型</label>
<div class="relative">
<select v-model="streamerForm.category" class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none appearance-none">
<option value="">請選擇直播類型</option>
<option value="gaming">遊戲直播</option>
<option value="chat">聊天互動</option>
<option value="music">音樂表演</option>
<option value="outdoor">戶外直播</option>
<option value="food">美食直播</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
<i class="fas fa-chevron-down text-gray-400"></i>
</div>
</div>
</div>
<!-- Specialties -->
<div>
<label class="block text-sm text-gray-400 mb-1">特長領域</label>
<textarea
v-model="streamerForm.specialties"
rows="3"
placeholder="請描述你的特長和專業領域..."
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
></textarea>
</div>
<!-- Experience -->
<div>
<label class="block text-sm text-gray-400 mb-1">直播經驗</label>
<textarea
v-model="streamerForm.experience"
rows="3"
placeholder="請分享你的直播或相關經驗..."
class="w-full px-3 py-2 bg-gray-700 text-gray-100 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
></textarea>
</div>
<!-- ID Upload -->
<div>
<label class="block text-sm text-gray-400 mb-1">身份證明文件</label>
<div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-lg hover:border-gray-500 transition-colors">
<div class="space-y-1 text-center">
<i class="fas fa-upload text-gray-400 text-2xl mb-2"></i>
<div class="text-sm text-gray-400">
<label for="file-upload" class="relative cursor-pointer bg-gray-700 rounded-md font-medium text-blue-400 hover:text-blue-500">
<span>上傳文件</span>
<input id="file-upload" name="file-upload" type="file" class="sr-only" @change="handleFileUpload">
</label>
<p class="pl-1">或將文件拖放到此處</p>
</div>
<p class="text-xs text-gray-500">
支持 JPG, PNG, PDF 格式，文件大小不超過 10MB
</p>
</div>
</div>
<div v-if="streamerForm.idFile" class="mt-2 text-sm text-gray-400">
已選擇文件: {{ streamerForm.idFile.name }}
</div>
</div>
</form>
</div>
<div class="p-4 border-t border-gray-700 flex justify-end space-x-3">
<button @click="showStreamerApplyModal = false" class="px-4 py-2 text-gray-400 hover:text-gray-300 !rounded-button">
取消
</button>
<button @click="submitStreamerApplication" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 !rounded-button">
提交申請
</button>
</div>
</div>
</div>
<!-- Application Success Toast -->
<div v-if="showApplicationSuccess" class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center">
<i class="fas fa-check-circle mr-2"></i>
申請已受理，我們將在 3 個工作日內完成審核
</div>
</div>
</div>
</div>
<!-- 道具商城區 -->
<div class="props-section bg-gray-800 mt-3 p-4 shadow-md">
<div class="flex justify-between items-center mb-4">
<div class="flex items-center">
<i class="fas fa-gift text-purple-400 text-xl mr-2"></i>
<span class="text-gray-100 font-medium">我的道具</span>
</div>
<button @click="showPropsDetails = !showPropsDetails" class="text-gray-400">
<i :class="['fas', showPropsDetails ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
</button>
</div>
<div v-if="showPropsDetails" class="space-y-4">
<div class="flex space-x-4 overflow-x-auto pb-2">
<div v-for="prop in activeProps" :key="prop.id"
class="flex-shrink-0 w-16 text-center">
<div class="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2 relative">
<img :src="prop.icon" :alt="prop.name" class="w-12 h-12 object-contain">
<div v-if="prop.isActive" class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-800"></div>
</div>
<div class="text-xs text-gray-300 truncate">{{ prop.name }}</div>
</div>
</div>
<div class="grid grid-cols-2 gap-4">
<button @click="showMountShop = true" class="flex items-center justify-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-car text-pink-400 mr-2"></i>
<span class="text-gray-200">座駕商城</span>
</button>
<!-- 座駕商城模態框 -->
<div v-if="showMountShop" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-h-[90vh] rounded-lg overflow-hidden">
<!-- 頂部標題欄 -->
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">座駕商城</h3>
<button @click="showMountShop = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<!-- 分類選擇 -->
<div class="p-4 border-b border-gray-700 flex space-x-3 overflow-x-auto">
<button
v-for="category in mountCategories"
:key="category.id"
@click="selectedCategory = category.id"
:class="[
'px-4 py-1.5 rounded-full text-sm whitespace-nowrap transition-colors !rounded-button',
selectedCategory === category.id
? 'bg-blue-600 text-white'
: 'bg-gray-700 text-gray-300 hover:bg-gray-600'
]"
>
{{ category.name }}
</button>
</div>
<!-- 座駕列表 -->
<div class="p-4 overflow-y-auto" style="max-height: calc(90vh - 8rem)">
<div class="mount-grid">
<div
v-for="mount in filteredMounts"
:key="mount.id"
class="mount-card rounded-lg overflow-hidden cursor-pointer"
@click="viewMountDetail(mount)"
>
<div class="aspect-square bg-gray-900 flex items-center justify-center p-4">
<img :src="mount.icon" :alt="mount.name" class="w-full h-full object-contain">
</div>
<div class="p-3">
<div class="flex justify-between items-center mb-2">
<h4 class="font-medium text-gray-100">{{ mount.name }}</h4>
<span class="text-blue-400 flex items-center">
<i class="fas fa-gem text-xs mr-1"></i>
{{ mount.price }}
</span>
</div>
<p class="text-xs text-gray-400 line-clamp-2">{{ mount.description }}</p>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 座駕詳情模態框 -->
<div v-if="showMountDetail && selectedMount" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-w-md rounded-lg overflow-hidden">
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">座駕詳情</h3>
<button @click="showMountDetail = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<div class="p-4">
<div class="aspect-square bg-gray-900 rounded-lg flex items-center justify-center p-8 mb-4">
<img :src="selectedMount.icon" :alt="selectedMount.name" class="w-full h-full object-contain">
</div>
<h4 class="text-xl font-medium text-gray-100 mb-2">{{ selectedMount.name }}</h4>
<p class="text-gray-300 mb-4">{{ selectedMount.description }}</p>
<div class="bg-gray-700 rounded-lg p-3 mb-4">
<h5 class="text-sm font-medium text-gray-200 mb-2">特效說明</h5>
<ul class="space-y-1">
<li v-for="(effect, index) in selectedMount.effects" :key="index" class="text-sm text-gray-400 flex items-center">
<i class="fas fa-check text-green-400 mr-2 text-xs"></i>
{{ effect }}
</li>
</ul>
</div>
<div class="flex items-center justify-between mb-4">
<div class="text-gray-400">
價格：
<span class="text-blue-400 text-xl font-medium ml-1">
<i class="fas fa-gem text-sm mr-1"></i>
{{ selectedMount.price }}
</span>
</div>
<button
v-if="!selectedMount.isOwned"
@click="purchaseMount(selectedMount)"
class="px-6 py-2 bg-blue-600 text-white rounded-full text-sm hover:bg-blue-700 transition-colors !rounded-button"
>
購買
</button>
<span v-else class="px-4 py-1.5 bg-green-600 text-white rounded-full text-sm">
已擁有
</span>
</div>
</div>
</div>
</div>
<!-- 購買成功提示 -->
<div
v-if="showPurchaseSuccess"
class="fixed top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center"
>
<i class="fas fa-check-circle mr-2"></i>
購買成功
</div>
<button @click="showEffectShop = true" class="flex items-center justify-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors !rounded-button">
<i class="fas fa-magic text-blue-400 mr-2"></i>
<span class="text-gray-200">特效商城</span>
</button>
<!-- 特效商城模態框 -->
<div v-if="showEffectShop" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-h-[90vh] rounded-lg overflow-hidden">
<!-- 頂部標題欄 -->
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">特效商城</h3>
<button @click="showEffectShop = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<!-- 分類選擇 -->
<div class="p-4 border-b border-gray-700 flex space-x-3 overflow-x-auto">
<button
v-for="category in effectCategories"
:key="category.id"
@click="selectedEffectCategory = category.id"
:class="[
'px-4 py-1.5 rounded-full text-sm whitespace-nowrap transition-colors !rounded-button',
selectedEffectCategory === category.id
? 'bg-blue-600 text-white'
: 'bg-gray-700 text-gray-300 hover:bg-gray-600'
]"
>
{{ category.name }}
</button>
</div>
<!-- 特效列表 -->
<div class="p-4 overflow-y-auto" style="max-height: calc(90vh - 8rem)">
<div class="effect-grid">
<div
v-for="effect in filteredEffects"
:key="effect.id"
class="effect-card rounded-lg overflow-hidden cursor-pointer"
@click="viewEffectDetail(effect)"
>
<div class="aspect-square bg-gray-900 flex items-center justify-center p-4">
<img :src="effect.icon" :alt="effect.name" class="w-full h-full object-contain">
</div>
<div class="p-3">
<div class="flex justify-between items-center mb-2">
<h4 class="font-medium text-gray-100">{{ effect.name }}</h4>
<span class="text-blue-400 flex items-center">
<i class="fas fa-gem text-xs mr-1"></i>
{{ effect.price }}
</span>
</div>
<p class="text-xs text-gray-400 line-clamp-2">{{ effect.description }}</p>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 特效詳情模態框 -->
<div v-if="showEffectDetail && selectedEffect" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
<div class="bg-gray-800 w-11/12 max-w-md rounded-lg overflow-hidden">
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium text-gray-100">特效詳情</h3>
<button @click="showEffectDetail = false" class="text-gray-400 hover:text-gray-300">
<i class="fas fa-times text-xl"></i>
</button>
</div>
<div class="p-4">
<div class="aspect-square bg-gray-900 rounded-lg flex items-center justify-center p-8 mb-4">
<img :src="selectedEffect.icon" :alt="selectedEffect.name" class="w-full h-full object-contain">
</div>
<h4 class="text-xl font-medium text-gray-100 mb-2">{{ selectedEffect.name }}</h4>
<p class="text-gray-300 mb-4">{{ selectedEffect.description }}</p>
<div class="bg-gray-700 rounded-lg p-3 mb-4">
<h5 class="text-sm font-medium text-gray-200 mb-2">特效說明</h5>
<ul class="space-y-1">
<li v-for="(feature, index) in selectedEffect.features" :key="index" class="text-sm text-gray-400 flex items-center">
<i class="fas fa-check text-green-400 mr-2 text-xs"></i>
{{ feature }}
</li>
</ul>
</div>
<div class="flex items-center justify-between mb-4">
<div class="text-gray-400">
價格：
<span class="text-blue-400 text-xl font-medium ml-1">
<i class="fas fa-gem text-sm mr-1"></i>
{{ selectedEffect.price }}
</span>
</div>
<button
v-if="!selectedEffect.isOwned"
@click="purchaseEffect(selectedEffect)"
class="px-6 py-2 bg-blue-600 text-white rounded-full text-sm hover:bg-blue-700 transition-colors !rounded-button"
>
購買
</button>
<span v-else class="px-4 py-1.5 bg-green-600 text-white rounded-full text-sm">
已擁有
</span>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 內容分類標籤頁 -->
<div class="content-tabs bg-gray-800 mt-3 shadow-md">
<div class="flex border-b border-gray-700">
<button
v-for="(tab, index) in tabs"
:key="index"
@click="activeTab = tab.id"
:class="['flex-1 py-3 text-center text-sm font-medium', activeTab === tab.id ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400']"
>
{{ tab.label }}
</button>
</div>
</div>
<!-- 貼文列表 -->
<div v-if="activeTab === 'posts'" class="posts-container">
<div v-for="post in userPosts" :key="post.id" class="post-card bg-gray-800 mt-3 shadow-md overflow-hidden">
<div class="p-4">
<div class="flex justify-between items-center mb-3">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full overflow-hidden mr-3">
<img :src="userProfile.avatar" alt="用戶頭像" class="w-full h-full object-cover" />
</div>
<div>
<div class="font-medium text-gray-100">{{ userProfile.name }}</div>
<div class="text-xs text-gray-400">{{ post.time }}</div>
</div>
</div>
<button class="text-gray-400 cursor-pointer">
<i class="fas fa-ellipsis-h"></i>
</button>
</div>
<p class="text-gray-200 mb-3">{{ post.content }}</p>
<div v-if="post.image" class="rounded-lg overflow-hidden mb-3">
<img :src="post.image" alt="貼文圖片" class="w-full h-auto object-top" />
</div>
<div class="flex justify-between text-sm text-gray-400 pt-2 border-t border-gray-700">
<div class="flex items-center cursor-pointer">
<i class="far fa-heart mr-1"></i>
<span>{{ post.likes }} 喜歡</span>
</div>
<div class="flex items-center cursor-pointer">
<i class="far fa-comment mr-1"></i>
<span>{{ post.comments }} 評論</span>
</div>
<div class="flex items-center cursor-pointer">
<i class="far fa-share-square mr-1"></i>
<span>分享</span>
</div>
</div>
</div>
</div>
</div>
<!-- 相簿列表 -->
<div v-if="activeTab === 'live'" class="photo-album-container">
<div class="grid grid-cols-3 gap-0.5 bg-gray-900">
<div v-for="photo in displayPhotos" :key="photo.id" class="aspect-square overflow-hidden cursor-pointer">
<img :src="photo.url" :alt="photo.description" class="w-full h-full object-cover" @click="openPhotoDetail(photo)" />
</div>
</div>
<!-- 照片查看模態框 -->
<div v-if="showPhotoModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-95">
<div class="relative w-full h-full flex flex-col">
<!-- 頂部操作欄 -->
<div class="absolute top-0 left-0 right-0 flex justify-between items-center p-4 z-10">
<button @click="closePhotoModal" class="text-white hover:text-gray-300 cursor-pointer">
<i class="fas fa-times text-2xl"></i>
</button>
<div class="flex items-center space-x-4">
<button class="text-white hover:text-gray-300 cursor-pointer">
<i class="fas fa-share-alt text-xl"></i>
</button>
<button class="text-white hover:text-gray-300 cursor-pointer">
<i class="fas fa-download text-xl"></i>
</button>
<button class="text-white hover:text-gray-300 cursor-pointer">
<i class="fas fa-heart text-xl"></i>
</button>
</div>
</div>
<!-- 照片展示區 -->
<div class="flex-1 flex items-center justify-center p-4">
<button @click="showPrevPhoto" class="absolute left-4 text-white hover:text-gray-300 cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed': currentPhotoIndex === 0 }">
<i class="fas fa-chevron-left text-3xl"></i>
</button>
<img :src="currentPhoto.url" :alt="currentPhoto.description" class="max-h-full max-w-full object-contain" />
<button @click="showNextPhoto" class="absolute right-4 text-white hover:text-gray-300 cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed': currentPhotoIndex === photos.length - 1 }">
<i class="fas fa-chevron-right text-3xl"></i>
</button>
</div>
<!-- 底部信息欄 -->
<div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
<p class="text-white text-lg font-medium mb-2">{{ currentPhoto.description }}</p>
<p class="text-gray-300 text-sm">拍攝於 2025年5月12日</p>
</div>
</div>
</div>
<!-- 加載更多 -->
<div v-if="hasMorePhotos" class="py-4 text-center" v-intersection-observer="loadMorePhotos">
<div class="inline-flex items-center text-gray-400 text-sm">
<i class="fas fa-spinner fa-spin mr-2"></i>
載入更多...
</div>
</div>
</div>
<!-- 粉絲列表 -->
<div v-if="activeTab === 'fans'" class="fans-container">
<div v-for="fan in userFans" :key="fan.id" class="fan-card bg-gray-800 mt-3 shadow-md p-4">
<div class="flex items-center justify-between">
<div class="flex items-center flex-1">
<div class="w-12 h-12 rounded-full overflow-hidden mr-3">
<img :src="fan.avatar" :alt="fan.name" class="w-full h-full object-cover" />
</div>
<div class="flex-1 min-w-0">
<div class="flex items-center">
<h3 class="font-medium text-gray-100 truncate">{{ fan.name }}</h3>
<span class="text-xs text-gray-400 ml-2">{{ fan.followTime }}</span>
</div>
<p class="text-sm text-gray-400 mt-0.5">@{{ fan.username }}</p>
<p class="text-sm text-gray-300 mt-1 truncate">{{ fan.bio }}</p>
</div>
</div>
<button class="ml-4 px-4 py-1.5 border border-blue-500 text-blue-400 text-sm rounded-full hover:bg-blue-900 hover:bg-opacity-30 transition-colors cursor-pointer !rounded-button">
回關
</button>
</div>
</div>
</div>
<!-- 沒有內容時顯示 -->
<div v-if="isEmptyTab" class="empty-state flex flex-col items-center justify-center py-16 px-4 bg-gray-800 mt-3">
<div class="w-20 h-20 rounded-full bg-gray-700 flex items-center justify-center mb-4">
<i class="far fa-folder-open text-3xl text-gray-500"></i>
</div>
<h3 class="text-gray-200 font-medium mb-2">暫無內容</h3>
<p class="text-gray-400 text-sm text-center">這裡還沒有任何內容，開始創建或收藏吧！</p>
<button class="mt-4 px-6 py-2 bg-blue-600 text-white rounded-full text-sm cursor-pointer !rounded-button">
立即創建
</button>
</div>
</main>
<!-- 底部導航欄 -->
<nav class="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 z-40">
<div class="grid grid-cols-5 h-14">
<a href="#" class="flex flex-col items-center justify-center text-gray-400 cursor-pointer no-underline">
<i class="fas fa-home text-xl"></i>
<span class="text-xs mt-1">首頁</span>
</a>
<button class="flex flex-col items-center justify-center text-gray-400 cursor-pointer">
<i class="fas fa-search text-xl"></i>
<span class="text-xs mt-1">搜尋</span>
</button>
<button class="flex flex-col items-center justify-center text-gray-400 cursor-pointer">
<i class="fas fa-plus-square text-xl"></i>
<span class="text-xs mt-1">發文</span>
</button>
<button class="flex flex-col items-center justify-center text-gray-400 cursor-pointer">
<i class="fas fa-bell text-xl"></i>
<span class="text-xs mt-1">通知</span>
</button>
<button class="flex flex-col items-center justify-center text-blue-400 cursor-pointer">
<i class="fas fa-user text-xl"></i>
<span class="text-xs mt-1">我的</span>
</button>
</div>
</nav>
</div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
// 用戶資料
const userProfile = ref({
name: '陳雅婷',
username: 'yating_chen',
avatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20portrait%2C%20professional%20headshot%2C%20soft%20natural%20lighting%2C%20neutral%20background%2C%20clear%20facial%20features%2C%20friendly%20smile%2C%20high%20quality%20photorealistic%20image%2C%20front%20facing%2C%20business%20casual%20attire%2C%20shoulder%20up%20framing&width=200&height=200&seq=11&orientation=squarish',
bio: '熱愛攝影與旅行的科技愛好者 | 台北科技大學資訊工程系 | 喜歡分享生活點滴和美食心得',
location: '台北市',
joinDate: '2023 年 8 月加入'
});
// 用戶統計數據
const userStats = ref([
{ label: '貼文', value: '38' },
{ label: '粉絲', value: '256' },
{ label: '追蹤', value: '184' },
{ label: '獲讚', value: '1.2k' }
]);
// 錢包相關狀態
const showWalletDetails = ref(false);
const showRechargeModal = ref(false);
const showPaymentProcessing = ref(false);
const showRechargeSuccess = ref(false);
const selectedAmount = ref('');
const selectedPayment = ref('');
const rechargeOptions = ref([
{ amount: 100, price: 100, bonus: 0 },
{ amount: 500, price: 500, bonus: 50 },
{ amount: 1000, price: 1000, bonus: 150 },
{ amount: 2000, price: 2000, bonus: 400 },
{ amount: 5000, price: 5000, bonus: 1250 },
{ amount: 10000, price: 10000, bonus: 3000 }
]);
const paymentMethods = ref([
{ id: 'credit', name: '信用卡', icon: 'fa-credit-card' },
{ id: 'line', name: 'Line Pay', icon: 'fa-line' },
{ id: 'apple', name: 'Apple Pay', icon: 'fa-apple' }
]);
const handleRecharge = () => {
if (!selectedAmount.value || !selectedPayment.value) return;
showRechargeModal.value = false;
showPaymentProcessing.value = true;
// Simulate payment process
setTimeout(() => {
showPaymentProcessing.value = false;
showRechargeSuccess.value = true;
// Reset selections
selectedAmount.value = '';
selectedPayment.value = '';
// Hide success message after 3 seconds
setTimeout(() => {
showRechargeSuccess.value = false;
}, 3000);
}, 2000);
};
const showWithdrawModal = ref(false);
const showWithdrawProcessing = ref(false);
const showWithdrawSuccess = ref(false);
const withdrawForm = ref({
amount: '',
method: '',
accountName: '',
accountNumber: ''
});
const accountPlaceholder = computed(() => {
switch(withdrawForm.value.method) {
case 'bank':
return '銀行卡號';
case 'alipay':
return '支付寶賬號';
case 'wechat':
return '微信賬號';
default:
return '收款賬號';
}
});
const isWithdrawFormValid = computed(() => {
return withdrawForm.value.amount &&
Number(withdrawForm.value.amount) >= 100 &&
Number(withdrawForm.value.amount) <= 50000 &&
withdrawForm.value.method &&
withdrawForm.value.accountName &&
withdrawForm.value.accountNumber;
});
const submitWithdrawal = () => {
if (!isWithdrawFormValid.value) return;
showWithdrawModal.value = false;
showWithdrawProcessing.value = true;
// Simulate API call
setTimeout(() => {
showWithdrawProcessing.value = false;
showWithdrawSuccess.value = true;
// Reset form
withdrawForm.value = {
amount: '',
method: '',
accountName: '',
accountNumber: ''
};
// Hide success message after 3 seconds
setTimeout(() => {
showWithdrawSuccess.value = false;
}, 3000);
}, 2000);
};
const showStreamerApplyModal = ref(false);
const isStreamer = ref(false);
const showApplicationSuccess = ref(false);
const streamerForm = ref({
category: '',
specialties: '',
experience: '',
idFile: null as File | null
});
const handleFileUpload = (event: Event) => {
const input = event.target as HTMLInputElement;
if (input.files && input.files[0]) {
streamerForm.value.idFile = input.files[0];
}
};
const submitStreamerApplication = () => {
// Here you would typically send the form data to your backend
// For now, we'll just simulate a successful submission
showStreamerApplyModal.value = false;
showApplicationSuccess.value = true;
// Reset form
streamerForm.value = {
category: '',
specialties: '',
experience: '',
idFile: null
};
// Hide success message after 3 seconds
setTimeout(() => {
showApplicationSuccess.value = false;
}, 3000);
};
const walletInfo = ref({
balance: '8,888',
withdrawable: '2,345'
});
// 道具相關狀態
const showPropsDetails = ref(false);
const showMountShop = ref(false);
const showEffectShop = ref(false);
const showEffectDetail = ref(false);
const selectedEffect = ref<any>(null);
const selectedEffectCategory = ref('all');
const effectCategories = ref([
{ id: 'all', name: '全部' },
{ id: 'entrance', name: '入場特效' },
{ id: 'chat', name: '聊天特效' },
{ id: 'gift', name: '禮物特效' },
{ id: 'level', name: '等級特效' }
]);
const effects = ref([
{
id: 1,
name: '星光入場',
icon: 'https://readdy.ai/api/search-image?query=Magical%20star%20entrance%20effect%20icon%2C%20sparkling%20particles%2C%20fantasy%20game%20asset%2C%20centered%20composition%2C%20isolated%20on%20dark%20background%2C%20glowing%20effect&width=100&height=100&seq=301&orientation=squarish',
price: 3888,
description: '華麗的星光入場特效，進入直播間時散發耀眼星光。',
category: 'entrance',
features: ['星光粒子', '閃耀光環', '專屬音效'],
isOwned: false
},
{
id: 2,
name: '彩虹聊天',
icon: 'https://readdy.ai/api/search-image?query=Rainbow%20chat%20bubble%20effect%20icon%2C%20colorful%20gradient%2C%20game%20UI%20asset%2C%20centered%20composition%2C%20isolated%20on%20dark%20background%2C%20glowing%20effect&width=100&height=100&seq=302&orientation=squarish',
price: 2888,
description: '絢麗的彩虹聊天特效，讓你的消息更加搶眼。',
category: 'chat',
features: ['彩虹漸變', '氣泡特效', '打字動畫'],
isOwned: true
},
{
id: 3,
name: '禮物煙花',
icon: 'https://readdy.ai/api/search-image?query=Firework%20gift%20effect%20icon%2C%20explosive%20particles%2C%20celebration%20animation%20asset%2C%20centered%20composition%2C%20isolated%20on%20dark%20background%2C%20glowing%20effect&width=100&height=100&seq=303&orientation=squarish',
price: 4888,
description: '絢爛的煙花特效，送禮時綻放絢麗的煙花。',
category: 'gift',
features: ['煙花綻放', '粒子特效', '禮物光環'],
isOwned: false
}
]);
const filteredEffects = computed(() => {
if (selectedEffectCategory.value === 'all') {
return effects.value;
}
return effects.value.filter(effect => effect.category === selectedEffectCategory.value);
});
const viewEffectDetail = (effect: any) => {
selectedEffect.value = effect;
showEffectDetail.value = true;
};
const purchaseEffect = (effect: any) => {
effect.isOwned = true;
showPurchaseSuccess.value = true;
setTimeout(() => {
showPurchaseSuccess.value = false;
showEffectDetail.value = false;
}, 2000);
};
const showMountDetail = ref(false);
const selectedMount = ref<any>(null);
const showPurchaseSuccess = ref(false);
const mountCategories = ref([
{ id: 'all', name: '全部' },
{ id: 'vehicle', name: '座駕' },
{ id: 'flying', name: '飛行' },
{ id: 'special', name: '特殊' }
]);
const selectedCategory = ref('all');
const mounts = ref([
{
id: 1,
name: '星光座駕',
icon: 'https://readdy.ai/api/search-image?query=Magical%20glowing%20star%20vehicle%20icon%2C%20fantasy%20game%20asset%2C%20sparkle%20effects%2C%20ethereal%20design%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=201&orientation=squarish',
price: 6888,
description: '奢華的星光座駕，散發著夢幻的星光效果，讓你在直播間閃耀奪目。',
category: 'vehicle',
effects: ['入場特效', '專屬座駕標識', '聊天特效'],
isOwned: true
},
{
id: 2,
name: '幻彩蝶翼',
icon: 'https://readdy.ai/api/search-image?query=Ethereal%20butterfly%20wings%20mount%2C%20rainbow%20colors%2C%20magical%20fantasy%20design%2C%20sparkle%20effects%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=202&orientation=squarish',
price: 8888,
description: '絢麗的蝶翼座駕，展翅時會釋放七彩光芒，讓你翱翔在直播間的星空中。',
category: 'flying',
effects: ['飛行特效', '翅膀光環', '彩蝶飄落'],
isOwned: false
},
{
id: 3,
name: '機甲戰神',
icon: 'https://readdy.ai/api/search-image?query=Futuristic%20mecha%20warrior%20mount%2C%20sci-fi%20design%2C%20glowing%20energy%20effects%2C%20metallic%20texture%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=203&orientation=squarish',
price: 12888,
description: '科技感十足的機甲座駕，配備粒子引擎，釋放震撼的機械特效。',
category: 'special',
effects: ['機甲變形', '引擎光效', '電子音效'],
isOwned: false
}
]);
const activeProps = ref([
{
id: 1,
name: '星光座駕',
icon: 'https://readdy.ai/api/search-image?query=Magical%20glowing%20star%20vehicle%20icon%2C%20fantasy%20game%20asset%2C%20sparkle%20effects%2C%20ethereal%20design%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=201&orientation=squarish',
isActive: true
},
{
id: 2,
name: '彩虹光環',
icon: 'https://readdy.ai/api/search-image?query=Rainbow%20halo%20effect%20icon%2C%20circular%20gradient%2C%20magical%20glow%2C%20fantasy%20game%20asset%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=202&orientation=squarish',
isActive: false
},
{
id: 3,
name: '閃電特效',
icon: 'https://readdy.ai/api/search-image?query=Lightning%20effect%20icon%2C%20electric%20blue%20glow%2C%20energy%20burst%2C%20fantasy%20game%20asset%2C%20centered%20composition%2C%20isolated%20on%20dark%20background&width=100&height=100&seq=203&orientation=squarish',
isActive: false
}
]);
// 標籤頁
const tabs = ref([
{ id: 'posts', label: '貼文' },
{ id: 'live', label: '相簿' },
{ id: 'fans', label: '粉絲' }
]);
const activeTab = ref('posts');
// 用戶粉絲列表
const userFans = ref([
{
id: '1',
name: '王小明',
username: 'xiaoming_wang',
avatar: 'https://readdy.ai/api/search-image?query=Young%20Asian%20male%20portrait%2C%20professional%20headshot%2C%20natural%20smile%2C%20casual%20style%2C%20clean%20background%2C%20front%20facing%2C%20high%20quality%20photo&width=100&height=100&seq=31&orientation=squarish',
bio: '攝影愛好者 | 美食探索家',
followTime: '2 天前'
},
{
id: '2',
name: '林美玲',
username: 'mei_lin',
avatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20portrait%2C%20casual%20style%2C%20friendly%20expression%2C%20natural%20lighting%2C%20simple%20background%2C%20front%20facing%2C%20high%20quality%20photo&width=100&height=100&seq=32&orientation=squarish',
bio: '旅行部落客 | 生活美學家',
followTime: '1 週前'
},
{
id: '3',
name: '張建國',
username: 'jianguo_zhang',
avatar: 'https://readdy.ai/api/search-image?query=Middle%20aged%20Asian%20male%20portrait%2C%20professional%20look%2C%20warm%20smile%2C%20business%20casual%2C%20clean%20background%2C%20front%20facing%2C%20high%20quality%20photo&width=100&height=100&seq=33&orientation=squarish',
bio: '科技愛好者 | 咖啡達人',
followTime: '2 週前'
}
]);
// 用戶貼文
const userPosts = ref([
{
id: '1',
content: '今天在信義區發現了一家超棒的咖啡廳，環境很舒適，咖啡也很香醇。最喜歡他們的肉桂捲，外酥內軟，甜度適中，配上一杯拿鐵簡直完美！',
image: 'https://readdy.ai/api/search-image?query=Cozy%20cafe%20interior%20in%20Taipei%20with%20stylish%20decor%2C%20warm%20lighting%2C%20comfortable%20seating%2C%20coffee%20cups%20on%20wooden%20tables%2C%20pastries%20display%2C%20modern%20minimalist%20design%2C%20soft%20ambient%20atmosphere%2C%20high%20quality%20photography&width=600&height=400&seq=12&orientation=landscape',
time: '2 小時前',
likes: 24,
comments: 5
},
{
id: '2',
content: '週末去了陽明山健行，天氣超好！沿途風景優美，還看到了許多野生動物。登頂後的視野真的很震撼，可以俯瞰整個台北盆地。',
image: 'https://readdy.ai/api/search-image?query=Yangmingshan%20National%20Park%20hiking%20trail%20with%20lush%20green%20vegetation%2C%20mountain%20view%20overlooking%20Taipei%20basin%2C%20clear%20blue%20sky%2C%20natural%20landscape%2C%20hikers%20enjoying%20the%20view%2C%20beautiful%20Taiwan%20mountain%20scenery%2C%20high%20quality%20travel%20photography&width=600&height=400&seq=13&orientation=landscape',
time: '昨天',
likes: 56,
comments: 8
},
{
id: '3',
content: '剛買了新的相機鏡頭，迫不及待想去試拍了！這次選擇了一個大光圈定焦鏡，希望能拍出更多好看的人像照。',
image: '',
time: '3 天前',
likes: 32,
comments: 6
}
]);
// 相簿數據和狀態管理
const photos = ref([
{
id: '1',
url: 'https://readdy.ai/api/search-image?query=Beautiful%20urban%20landscape%20photography%20of%20Taipei%20city%20at%20sunset%2C%20vibrant%20colors%2C%20architectural%20details%2C%20high%20quality%20professional%20photo%20with%20dramatic%20lighting%20and%20composition&width=400&height=400&seq=101&orientation=squarish',
description: '台北城市風景'
},
{
id: '2',
url: 'https://readdy.ai/api/search-image?query=Artistic%20food%20photography%20of%20traditional%20Taiwanese%20street%20food%2C%20close%20up%20details%2C%20beautiful%20plating%2C%20soft%20natural%20lighting%2C%20professional%20food%20styling%2C%20high%20quality%20image&width=400&height=400&seq=102&orientation=squarish',
description: '美食攝影'
},
{
id: '3',
url: 'https://readdy.ai/api/search-image?query=Portrait%20photography%20in%20natural%20setting%2C%20soft%20bokeh%20background%2C%20perfect%20lighting%2C%20candid%20moment%2C%20professional%20quality%2C%20artistic%20composition&width=400&height=400&seq=103&orientation=squarish',
description: '人像攝影'
},
{
id: '4',
url: 'https://readdy.ai/api/search-image?query=Nature%20photography%20of%20Taiwan%20mountain%20scenery%2C%20morning%20mist%2C%20dramatic%20lighting%2C%20professional%20landscape%20photo%2C%20high%20detail%20quality&width=400&height=400&seq=104&orientation=squarish',
description: '自然風景'
},
{
id: '5',
url: 'https://readdy.ai/api/search-image?query=Street%20photography%20of%20Taipei%20night%20market%2C%20vibrant%20colors%2C%20capturing%20daily%20life%2C%20professional%20documentary%20style%20photo&width=400&height=400&seq=105&orientation=squarish',
description: '街頭攝影'
},
{
id: '6',
url: 'https://readdy.ai/api/search-image?query=Architectural%20photography%20of%20modern%20building%20interior%2C%20geometric%20patterns%2C%20dramatic%20lighting%2C%20professional%20quality%20photo&width=400&height=400&seq=106&orientation=squarish',
description: '建築攝影'
},
{
id: '7',
url: 'https://readdy.ai/api/search-image?query=Minimalist%20still%20life%20photography%2C%20simple%20composition%2C%20perfect%20lighting%2C%20professional%20product%20photo%20style&width=400&height=400&seq=107&orientation=squarish',
description: '靜物攝影'
},
{
id: '8',
url: 'https://readdy.ai/api/search-image?query=Travel%20photography%20of%20traditional%20temple%20in%20Taiwan%2C%20rich%20cultural%20details%2C%20golden%20hour%20lighting%2C%20professional%20architecture%20photo&width=400&height=400&seq=108&orientation=squarish',
description: '寺廟建築'
},
{
id: '9',
url: 'https://readdy.ai/api/search-image?query=Macro%20photography%20of%20flowers%2C%20extreme%20detail%2C%20soft%20natural%20lighting%2C%20professional%20nature%20photo&width=400&height=400&seq=109&orientation=squarish',
description: '微距攝影'
},
{
id: '10',
url: 'https://readdy.ai/api/search-image?query=Urban%20night%20photography%20of%20Taipei%20city%20lights%2C%20long%20exposure%2C%20vibrant%20colors%2C%20professional%20cityscape%20photo&width=400&height=400&seq=110&orientation=squarish',
description: '城市夜景'
},
{
id: '11',
url: 'https://readdy.ai/api/search-image?query=Taiwanese%20tea%20ceremony%20photography%2C%20traditional%20culture%2C%20soft%20lighting%2C%20professional%20documentary%20style&width=400&height=400&seq=111&orientation=squarish',
description: '茶藝文化'
},
{
id: '12',
url: 'https://readdy.ai/api/search-image?query=Coastal%20landscape%20photography%20of%20Taiwan%20eastern%20coast%2C%20dramatic%20waves%2C%20sunrise%2C%20professional%20nature%20photo&width=400&height=400&seq=112&orientation=squarish',
description: '海岸風光'
}
]);
const page = ref(1);
const perPage = 9;
const hasMorePhotos = ref(true);
const displayPhotos = ref(photos.value.slice(0, perPage));
// 加載更多照片
const loadMorePhotos = () => {
const nextPage = page.value + 1;
const start = (nextPage - 1) * perPage;
const end = start + perPage;
const newPhotos = photos.value.slice(start, end);
if (newPhotos.length > 0) {
displayPhotos.value = [...displayPhotos.value, ...newPhotos];
page.value = nextPage;
} else {
hasMorePhotos.value = false;
}
};
// 照片查看相關狀態
const showPhotoModal = ref(false);
const currentPhotoIndex = ref(0);
const currentPhoto = computed(() => photos.value[currentPhotoIndex.value]);
// 查看照片詳情
const openPhotoDetail = (photo: any) => {
currentPhotoIndex.value = photos.value.findIndex(p => p.id === photo.id);
showPhotoModal.value = true;
};
// 切換照片
const showPrevPhoto = () => {
if (currentPhotoIndex.value > 0) {
currentPhotoIndex.value--;
}
};
const showNextPhoto = () => {
if (currentPhotoIndex.value < photos.value.length - 1) {
currentPhotoIndex.value++;
}
};
// 關閉模態框
const closePhotoModal = () => {
showPhotoModal.value = false;
};
// 計算當前標籤頁是否為空
const isEmptyTab = computed(() => {
if (activeTab.value === 'posts' && userPosts.value.length === 0) return true;
if (activeTab.value === 'live' && photos.value.length === 0) return true;
if (activeTab.value === 'fans' && userFans.value.length === 0) return true;
return false;
});
// 編輯模態框相關狀態
const showEditModal = ref(false);
const showSuccessToast = ref(false);
const showAvatarModal = ref(false);
const showMoreMenu = ref(false);
// 下拉菜單選項
const menuItems = ref([
{
id: 'settings',
label: '設置',
icon: 'fas fa-cog',
action: 'settings'
},
{
id: 'switch',
label: '切換賬號',
icon: 'fas fa-sync-alt',
action: 'switchAccount'
},
{
id: 'share',
label: '分享個人主頁',
icon: 'fas fa-share-alt',
action: 'shareProfile'
},
{
id: 'blacklist',
label: '黑名單管理',
icon: 'fas fa-ban',
action: 'manageBlacklist'
},
{
id: 'logout',
label: '登出',
icon: 'fas fa-sign-out-alt',
action: 'logout'
}
]);
// 處理菜單操作
const handleMenuAction = (action: string) => {
showMoreMenu.value = false;
switch (action) {
case 'settings':
// 跳轉到設置頁面
console.log('Navigate to settings');
break;
case 'switchAccount':
// 切換賬號邏輯
console.log('Switch account');
break;
case 'shareProfile':
// 分享個人主頁邏輯
console.log('Share profile');
break;
case 'manageBlacklist':
// 跳轉到黑名單管理頁面
console.log('Manage blacklist');
break;
case 'logout':
// 登出邏輯
console.log('Logout');
break;
}
};
const showAvatarCropModal = ref(false);
const showAvatarSuccessToast = ref(false);
const tempAvatarUrl = ref('');
// 處理頭像上傳
const handleAvatarUpload = (type: 'album' | 'camera') => {
showAvatarModal.value = false;
// 模擬選擇圖片
tempAvatarUrl.value = userProfile.value.avatar;
showAvatarCropModal.value = true;
};
// 取消頭像裁剪
const cancelAvatarCrop = () => {
showAvatarCropModal.value = false;
tempAvatarUrl.value = '';
};
// 確認頭像上傳
const confirmAvatarUpload = () => {
// 模擬上傳成功
userProfile.value.avatar = tempAvatarUrl.value;
showAvatarCropModal.value = false;
tempAvatarUrl.value = '';
// 顯示成功提示
showAvatarSuccessToast.value = true;
setTimeout(() => {
showAvatarSuccessToast.value = false;
}, 2000);
};
const editForm = ref({
name: userProfile.value.name,
username: userProfile.value.username,
bio: userProfile.value.bio,
location: userProfile.value.location,
birthday: '',
gender: ''
});
// 保存個人資料
const saveProfile = () => {
// 更新個人資料
userProfile.value = {
...userProfile.value,
name: editForm.value.name,
username: editForm.value.username,
bio: editForm.value.bio,
location: editForm.value.location
};
// 關閉模態框
showEditModal.value = false;
// 顯示成功提示
showSuccessToast.value = true;
setTimeout(() => {
showSuccessToast.value = false;
}, 2000);
};
// 創建一個空的userLives引用以避免錯誤
const userLives = ref([]);
// 購買座駕
const purchaseMount = (mount: any) => {
// 模擬購買邏輯
mount.isOwned = true;
showPurchaseSuccess.value = true;
setTimeout(() => {
showPurchaseSuccess.value = false;
showMountDetail.value = false;
}, 2000);
};
// 查看座駕詳情
const viewMountDetail = (mount: any) => {
selectedMount.value = mount;
showMountDetail.value = true;
};
// 篩選座駕
const filteredMounts = computed(() => {
if (selectedCategory.value === 'all') {
return mounts.value;
}
return mounts.value.filter(mount => mount.category === selectedCategory.value);
});
</script>
<style scoped>
.app-container {
font-family: 'Noto Sans TC', sans-serif;
}
.mount-grid,
.effect-grid {
display: grid;
grid-template-columns: repeat(2, 1fr);
gap: 1rem;
}
.effect-card {
background: linear-gradient(to bottom right, rgba(55, 65, 81, 0.5), rgba(17, 24, 39, 0.7));
border: 1px solid rgba(75, 85, 99, 0.4);
transition: transform 0.2s ease;
}
.effect-card:hover {
transform: translateY(-2px);
}
.mount-card {
background: linear-gradient(to bottom right, rgba(55, 65, 81, 0.5), rgba(17, 24, 39, 0.7));
border: 1px solid rgba(75, 85, 99, 0.4);
transition: transform 0.2s ease;
}
.mount-card:hover {
transform: translateY(-2px);
}
.profile-card {
background-image: linear-gradient(to bottom, rgba(30, 41, 59, 0.7), rgba(30, 41, 59, 1));
}
.line-clamp-2 {
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}
/* 自定義樣式 */
.profile__stats-item {
position: relative;
}
.profile__stats-item:not(:last-child)::after {
content: '';
position: absolute;
right: 0;
top: 25%;
height: 50%;
width: 1px;
background-color: #374151;
}
.post-card {
transition: all 0.2s ease;
}
.post-card:hover {
transform: translateY(-2px);
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.collection-card {
transition: all 0.2s ease;
}
.collection-card:hover {
transform: translateY(-2px);
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
</style>
