<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生產環境 JWT 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LiveShow 生產環境 JWT 測試</h1>
        
        <div class="test-section">
            <h3>1. 測試登入並獲取 Token</h3>
            <input type="text" id="username" placeholder="用戶名" value="kara_howe299">
            <input type="text" id="password" placeholder="密碼" value="password123">
            <button onclick="testLogin()">登入測試</button>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>2. 測試 JWT 驗證</h3>
            <input type="text" id="token" placeholder="JWT Token (從登入獲取)">
            <button onclick="testAuth()">驗證 Token</button>
            <div id="authResult"></div>
        </div>

        <div class="test-section">
            <h3>3. 測試健康檢查</h3>
            <button onclick="testHealth()">健康檢查</button>
            <div id="healthResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://liveshow.sotime.app/api';

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ 登入成功</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    // 自動填入 token
                    if (data.token) {
                        document.getElementById('token').value = data.token;
                    }
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `
                        <h4>❌ 登入失敗</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h4>❌ 網絡錯誤</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testAuth() {
            const token = document.getElementById('token').value;
            const resultDiv = document.getElementById('authResult');
            
            if (!token) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = '<h4>❌ 請先輸入 Token</h4>';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ JWT 驗證成功</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `
                        <h4>❌ JWT 驗證失敗 (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h4>❌ 網絡錯誤</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ 服務器健康</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `
                        <h4>❌ 服務器異常 (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h4>❌ 無法連接服務器</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
