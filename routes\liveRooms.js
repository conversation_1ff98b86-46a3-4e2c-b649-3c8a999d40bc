const express = require('express');
const router = express.Router();
const LiveRoom = require('../models/LiveRoom');
const Category = require('../models/Category');
const protect = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const { uploadToGCS, deleteFromGCS, getFileNameFromUrl } = require('../config/storage');

// 檔案上傳設定 - 使用記憶體存儲
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB 限制（封面圖片）
  },
  fileFilter: (req, file, cb) => {
    // 只允許圖片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳圖片文件'), false);
    }
  }
});

// 取得正在直播中的房間
// 支援 ?category=slug 參數，若為 all 或未提供則不過濾
router.get('/', async (req, res) => {
  try {
    const { category, page = 1, limit = 20, q } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    let query = { isLive: true };

    if (category && category !== 'all') {
      // 找到對應分類，使用 slug
      const catDoc = await Category.findOne({ slug: category });
      if (catDoc) {
        query.category = catDoc._id;
      } else {
        return res.status(400).json({ message: '分類不存在' });
      }
    }

    if (q) {
      const kw = new RegExp(q, 'i');
      query.$or = [
        { title: kw },
        { tags: kw },
      ];
    }

    const rooms = await LiveRoom.find(query)
      .populate('host', 'name avatar')
      .populate('category', 'name slug')
      .sort({ viewers: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    res.json(rooms);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得直播間失敗' });
  }
});

// 創建直播間
router.post('/', protect, upload.single('cover'), async (req, res) => {
  const { title, category, tags } = req.body;

  try {
    // 檢查分類是否存在
    const categoryDoc = await Category.findById(category);
    if (!categoryDoc) {
      return res.status(400).json({ message: '分類不存在' });
    }

    let coverUrl = '';

    // 如果有上傳封面圖片
    if (req.file) {
      const ext = path.extname(req.file.originalname);
      const fileName = `covers/${Date.now()}-${req.user._id}${ext}`;
      coverUrl = await uploadToGCS(req.file.buffer, fileName, req.file.mimetype);
    }

    // 處理標籤
    const tagsArray = tags ? (Array.isArray(tags) ? tags : tags.split(',').map(tag => tag.trim())) : [];

    const liveRoom = await LiveRoom.create({
      title,
      host: req.user._id,
      cover: coverUrl,
      category: categoryDoc._id,
      tags: tagsArray,
      isLive: true
    });

    await liveRoom.populate('host', 'name avatar');
    await liveRoom.populate('category', 'name slug');

    res.status(201).json(liveRoom);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '創建直播間失敗' });
  }
});

// 更新直播間
router.put('/:id', protect, upload.single('cover'), async (req, res) => {
  const { title, category, tags } = req.body;

  try {
    const liveRoom = await LiveRoom.findById(req.params.id);
    if (!liveRoom) return res.status(404).json({ message: '直播間不存在' });

    // 確保只有主播能更新自己的直播間
    if (liveRoom.host.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限更新此直播間' });
    }

    // 檢查分類是否存在
    if (category) {
      const categoryDoc = await Category.findById(category);
      if (!categoryDoc) {
        return res.status(400).json({ message: '分類不存在' });
      }
      liveRoom.category = categoryDoc._id;
    }

    // 如果有上傳新封面圖片
    if (req.file) {
      // 刪除舊封面（如果存在且是 GCS URL）
      if (liveRoom.cover && liveRoom.cover.includes('storage.googleapis.com')) {
        try {
          const oldFileName = getFileNameFromUrl(liveRoom.cover);
          await deleteFromGCS(oldFileName);
        } catch (deleteError) {
          console.error('刪除舊封面失敗:', deleteError);
          // 繼續執行，不中斷流程
        }
      }

      const ext = path.extname(req.file.originalname);
      const fileName = `covers/${Date.now()}-${req.user._id}${ext}`;
      liveRoom.cover = await uploadToGCS(req.file.buffer, fileName, req.file.mimetype);
    }

    // 更新其他字段
    if (title) liveRoom.title = title;
    if (tags) {
      const tagsArray = Array.isArray(tags) ? tags : tags.split(',').map(tag => tag.trim());
      liveRoom.tags = tagsArray;
    }

    await liveRoom.save();
    await liveRoom.populate('host', 'name avatar');
    await liveRoom.populate('category', 'name slug');

    res.json(liveRoom);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '更新直播間失敗' });
  }
});

// 刪除直播間
router.delete('/:id', protect, async (req, res) => {
  try {
    const liveRoom = await LiveRoom.findById(req.params.id);
    if (!liveRoom) return res.status(404).json({ message: '直播間不存在' });

    // 確保只有主播能刪除自己的直播間
    if (liveRoom.host.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限刪除此直播間' });
    }

    // 刪除封面圖片（如果存在且是 GCS URL）
    if (liveRoom.cover && liveRoom.cover.includes('storage.googleapis.com')) {
      try {
        const fileName = getFileNameFromUrl(liveRoom.cover);
        await deleteFromGCS(fileName);
      } catch (deleteError) {
        console.error('刪除封面失敗:', deleteError);
        // 繼續執行，不中斷流程
      }
    }

    await LiveRoom.deleteOne({ _id: req.params.id });
    res.json({ message: '直播間已刪除' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '刪除直播間失敗' });
  }
});

module.exports = router;