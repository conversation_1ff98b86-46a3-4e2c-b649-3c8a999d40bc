const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const protect = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const { uploadToGCS, deleteFromGCS, getFileNameFromUrl } = require('../config/storage');

// 檔案上傳設定 - 使用記憶體存儲
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB 限制（頭像文件較小）
  },
  fileFilter: (req, file, cb) => {
    // 只允許圖片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳圖片文件'), false);
    }
  }
});

// 註冊
router.post('/register', async (req, res) => {
  const { name, username, email, password, avatar } = req.body;
  try {
    if (await User.findOne({ $or: [{ email }, { username }] })) {
      return res.status(400).json({ message: '電子郵件或使用者名稱已被註冊' });
    }
    const user = await User.create({ name, username, email, password, avatar });
    const jwtSecret = process.env.JWT_SECRET || 'sayhong';
    const token = jwt.sign({ id: user._id }, jwtSecret, { expiresIn: '30d' });
    res.status(201).json({
      token,
      user: { id: user._id, name: user.name, username: user.username, avatar: user.avatar },
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '註冊失敗' });
  }
});

// 登入
router.post('/login', async (req, res) => {
  const { email, password } = req.body;
  try {
    const user = await User.findOne({ email });
    if (user && (await user.matchPassword(password))) {
      const jwtSecret = process.env.JWT_SECRET || 'sayhong';
      const token = jwt.sign({ id: user._id }, jwtSecret, { expiresIn: '30d' });
      res.json({
        token,
        user: { id: user._id, name: user.name, avatar: user.avatar },
      });
    } else {
      res.status(401).json({ message: '帳號或密碼錯誤' });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '登入失敗' });
  }
});

router.get('/me', protect, (req, res) => {
  res.json({ user: { id: req.user._id, name: req.user.name, username: req.user.username, avatar: req.user.avatar } });
});

// 更新用戶資料
router.put('/profile', protect, async (req, res) => {
  const { name, username } = req.body;

  try {
    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: '用戶不存在' });

    // 檢查用戶名是否已被其他用戶使用
    if (username && username !== user.username) {
      const existingUser = await User.findOne({ username });
      if (existingUser) {
        return res.status(400).json({ message: '用戶名已被使用' });
      }
    }

    // 更新用戶資料
    if (name) user.name = name;
    if (username) user.username = username;

    await user.save();

    res.json({
      user: {
        id: user._id,
        name: user.name,
        username: user.username,
        avatar: user.avatar
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '更新用戶資料失敗' });
  }
});

// 上傳用戶頭像
router.post('/avatar', protect, upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: '請選擇要上傳的頭像文件' });
    }

    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: '用戶不存在' });

    // 刪除舊頭像（如果存在且是 GCS URL）
    if (user.avatar && user.avatar.includes('storage.googleapis.com')) {
      try {
        const oldFileName = getFileNameFromUrl(user.avatar);
        await deleteFromGCS(oldFileName);
      } catch (deleteError) {
        console.error('刪除舊頭像失敗:', deleteError);
        // 繼續執行，不中斷流程
      }
    }

    // 上傳新頭像到 GCS
    const ext = path.extname(req.file.originalname);
    const fileName = `avatars/${Date.now()}-${req.user._id}${ext}`;

    const publicUrl = await uploadToGCS(req.file.buffer, fileName, req.file.mimetype);

    // 更新用戶頭像 URL
    user.avatar = publicUrl;
    await user.save();

    res.json({
      message: '頭像上傳成功',
      user: {
        id: user._id,
        name: user.name,
        username: user.username,
        avatar: user.avatar
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '頭像上傳失敗' });
  }
});

module.exports = router;