@echo off
setlocal enabledelayedexpansion

REM SoTime App Windows 部署腳本
REM 使用方法: deploy.bat [環境] [操作]
REM 環境: dev, staging, prod
REM 操作: build, start, stop, restart, logs, clean, status

set ENVIRONMENT=%1
set ACTION=%2
set APP_NAME=sotime-app
set COMPOSE_FILE=docker-compose.yml

if "%ENVIRONMENT%"=="" set ENVIRONMENT=prod
if "%ACTION%"=="" set ACTION=start

echo [INFO] SoTime App 部署腳本
echo [INFO] 環境: %ENVIRONMENT%, 操作: %ACTION%

REM 檢查 Docker 是否安裝
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安裝，請先安裝 Docker Desktop
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安裝，請先安裝 Docker Compose
    exit /b 1
)

echo [SUCCESS] 依賴檢查完成

if "%ACTION%"=="build" goto :build
if "%ACTION%"=="start" goto :start
if "%ACTION%"=="stop" goto :stop
if "%ACTION%"=="restart" goto :restart
if "%ACTION%"=="logs" goto :logs
if "%ACTION%"=="clean" goto :clean
if "%ACTION%"=="status" goto :status
if "%ACTION%"=="backup" goto :backup

echo [ERROR] 未知操作: %ACTION%
echo 可用操作: build, start, stop, restart, logs, clean, status, backup
exit /b 1

:build
echo [INFO] 構建 Docker 映像...
docker build -t %APP_NAME%:latest .
if errorlevel 1 (
    echo [ERROR] 映像構建失敗
    exit /b 1
)
echo [SUCCESS] 映像構建完成
goto :end

:start
echo [INFO] 啟動服務...

REM 創建必要的目錄
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

REM 複製環境配置
if exist ".env.%ENVIRONMENT%" (
    copy ".env.%ENVIRONMENT%" ".env" >nul
    echo [INFO] 使用 %ENVIRONMENT% 環境配置
) else (
    echo [WARNING] 未找到 .env.%ENVIRONMENT% 文件，使用默認配置
)

REM 啟動服務
docker-compose -f %COMPOSE_FILE% up -d
if errorlevel 1 (
    echo [ERROR] 服務啟動失敗
    exit /b 1
)

echo [INFO] 等待服務啟動...
timeout /t 30 /nobreak >nul

REM 健康檢查
call :health_check
if errorlevel 1 (
    echo [ERROR] 服務啟動失敗！
    docker-compose logs
    exit /b 1
) else (
    echo [SUCCESS] 服務啟動成功！
    call :show_status
)
goto :end

:stop
echo [INFO] 停止服務...
docker-compose -f %COMPOSE_FILE% down
echo [SUCCESS] 服務已停止
goto :end

:restart
echo [INFO] 重啟服務...
call :stop
call :start
goto :end

:logs
echo [INFO] 顯示服務日誌...
docker-compose -f %COMPOSE_FILE% logs -f --tail=100
goto :end

:clean
echo [WARNING] 清理 Docker 資源...
docker-compose -f %COMPOSE_FILE% down -v
docker image prune -f
docker container prune -f
docker network prune -f
echo [SUCCESS] 清理完成
goto :end

:status
call :show_status
goto :end

:backup
set backup_dir=backups\%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
mkdir "%backup_dir%" 2>nul

echo [INFO] 備份數據到 %backup_dir%...
echo [INFO] 注意：文件現在存儲在 GCP Storage，無需本地備份

echo [SUCCESS] 數據備份完成
goto :end

:health_check
set /a attempts=0
set /a max_attempts=10

:health_loop
set /a attempts+=1
curl -f http://localhost:6666/health >nul 2>&1
if not errorlevel 1 (
    exit /b 0
)

if %attempts% geq %max_attempts% (
    exit /b 1
)

echo [INFO] 健康檢查失敗，重試 (%attempts%/%max_attempts%)...
timeout /t 5 /nobreak >nul
goto :health_loop

:show_status
echo [INFO] 服務狀態:
docker-compose -f %COMPOSE_FILE% ps
echo.
echo [INFO] 訪問地址:
echo   🌐 應用: http://localhost:6666
echo   🌐 正式機: https://liveshow.sotime.app
echo   📊 健康檢查: http://localhost:6666/health
echo   🗄️ MongoDB: localhost:27017
echo   🔴 Redis: localhost:6379
goto :eof

:end
echo [INFO] 操作完成
