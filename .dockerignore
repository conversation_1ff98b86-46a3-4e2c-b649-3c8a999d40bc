# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 前端構建產物（會在 Docker 內重新構建）
client/dist/
client/node_modules/

# 環境變量文件
.env
.env.local
.env.development
.env.test
.env.production

# 日誌文件
logs/
*.log

# 運行時文件
*.pid
*.seed
*.pid.lock

# 覆蓋率報告
coverage/
.nyc_output

# 依賴目錄
.npm
.eslintcache

# 可選的 REPL 歷史
.node_repl_history

# 輸出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# 下一個.js 構建輸出
.next

# Nuxt.js 構建 / 生成輸出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Vuepress 構建輸出
.vuepress/dist

# Serverless 目錄
.serverless/

# FuseBox 緩存
.fusebox/

# DynamoDB 本地文件
.dynamodb/

# TernJS 端口文件
.tern-port

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# 開發工具
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系統
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 備份文件
backups/
*.backup
*.bak

# 測試文件
test/
tests/
__tests__/
*.test.js
*.spec.js

# 文檔
README.md
CHANGELOG.md
LICENSE
docs/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# 部署腳本
deploy.sh

# 臨時文件
tmp/
temp/
.tmp/
