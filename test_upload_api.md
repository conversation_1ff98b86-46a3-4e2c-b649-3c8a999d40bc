# 測試上傳 API 端點

## 問題修復

已修復以下問題：

1. **API 路徑重複問題**：
   - 錯誤：`/api/api/upload/limits`
   - 修正：`/upload/limits`
   - 原因：axios baseURL 已設定為 `/api`，不需要再加前綴

2. **圖片壓縮變數未定義錯誤**：
   - 錯誤：`currentQuality is not defined`
   - 修正：在 `canvas.toBlob` 最後參數使用 `quality` 而非 `currentQuality`

## 測試 API 端點

### 1. 測試上傳限制端點
```bash
curl -X GET https://liveshow.sotime.app/api/upload/limits \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

預期回應：
```json
{
  "message": "檔案上傳限制",
  "limits": {
    "image": {
      "maxSize": 10485760,
      "allowedTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"],
      "folder": "images"
    },
    "video": {
      "maxSize": 104857600,
      "allowedTypes": ["video/mp4", "video/webm", "video/quicktime", "video/x-msvideo"],
      "folder": "videos"
    }
  }
}
```

### 2. 測試生成上傳憑證
```bash
curl -X POST https://liveshow.sotime.app/api/upload/signed-url \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "files": [
      {
        "name": "test.jpg",
        "type": "image/jpeg",
        "size": 1024000,
        "fileType": "image"
      }
    ]
  }'
```

預期回應：
```json
{
  "message": "上傳憑證生成成功",
  "credentials": [
    {
      "originalName": "test.jpg",
      "fileName": "images/1234567890-userId-abc123.jpg",
      "signedUrl": "https://storage.googleapis.com/liveshowsotime/...",
      "publicUrl": "https://storage.googleapis.com/liveshowsotime/images/1234567890-userId-abc123.jpg",
      "mimeType": "image/jpeg",
      "fileType": "image"
    }
  ],
  "expiresIn": 900000
}
```

### 3. 測試檔案驗證
```bash
curl -X POST https://liveshow.sotime.app/api/upload/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "files": [
      {
        "fileName": "images/1234567890-userId-abc123.jpg",
        "publicUrl": "https://storage.googleapis.com/liveshowsotime/images/1234567890-userId-abc123.jpg",
        "originalName": "test.jpg",
        "mimeType": "image/jpeg",
        "fileType": "image"
      }
    ]
  }'
```

## 前端測試步驟

1. **開啟瀏覽器開發者工具**
2. **進入發文頁面**
3. **選擇圖片或影片檔案**
4. **觀察 Network 標籤**：
   - 應該看到 `/upload/limits` 請求成功
   - 應該看到 `/upload/signed-url` 請求成功
   - 應該看到直接上傳到 GCS 的請求
   - 應該看到 `/upload/verify` 請求成功

## 預期行為

### 成功流程：
1. 頁面載入時獲取上傳限制 ✅
2. 選擇檔案後進行壓縮 ✅
3. 獲取上傳憑證 ✅
4. 直接上傳到 GCS ✅
5. 驗證上傳完成 ✅
6. 發布貼文 ✅

### 錯誤處理：
- 檔案類型不支援 → 顯示錯誤訊息
- 檔案大小超限 → 顯示錯誤訊息
- 網路錯誤 → 顯示錯誤訊息並重試
- 上傳失敗 → 回退到原始檔案

## 效能監控

### 關鍵指標：
- **上傳速度**：應比之前快 50-80%
- **Server 負載**：應明顯降低
- **用戶體驗**：即時進度回饋
- **錯誤率**：應保持在低水平

### 監控方法：
1. 瀏覽器 Network 標籤查看請求時間
2. 後端日誌查看 API 回應時間
3. GCS 控制台查看上傳統計
4. 用戶回饋收集

## 故障排除

### 常見問題：

1. **404 錯誤**：
   - 檢查 API 路徑是否正確
   - 確認後端路由已正確註冊

2. **401 認證錯誤**：
   - 檢查 JWT Token 是否有效
   - 確認 Authorization Header 格式正確

3. **403 權限錯誤**：
   - 檢查 GCS 服務帳戶權限
   - 確認 Signed URL 生成正確

4. **檔案上傳失敗**：
   - 檢查檔案大小和類型限制
   - 確認 GCS Bucket 設定正確
   - 檢查網路連線狀況

## 下一步

如果測試成功，可以考慮：

1. **效能優化**：
   - 實現斷點續傳
   - 添加 CDN 加速
   - 優化壓縮算法

2. **功能擴展**：
   - 支援更多檔案格式
   - 添加批量操作
   - 實現進度持久化

3. **監控改進**：
   - 添加詳細的錯誤追蹤
   - 實現效能監控儀表板
   - 設定告警機制
