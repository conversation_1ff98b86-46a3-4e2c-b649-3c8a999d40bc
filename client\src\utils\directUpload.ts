/**
 * 直接上傳到 GCS 的工具
 * 使用 Signed URL 進行安全的直接上傳
 */

import axios from 'axios';

export interface FileUploadInfo {
  name: string;
  type: string;
  size: number;
  fileType: 'image' | 'video' | 'avatar' | 'cover' | 'banner';
}

export interface UploadCredential {
  originalName: string;
  fileName: string;
  signedUrl: string;
  publicUrl: string;
  mimeType: string;
  fileType: string;
}

export interface UploadResult {
  fileName: string;
  publicUrl: string;
  originalName: string;
  mimeType: string;
  fileType: string;
  verified: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  fileName: string;
}

/**
 * 獲取上傳憑證
 */
export async function getUploadCredentials(files: FileUploadInfo[]): Promise<UploadCredential[]> {
  try {
    const response = await axios.post('/upload/signed-url', { files });
    return response.data.credentials;
  } catch (error: any) {
    console.error('獲取上傳憑證失敗:', error);
    throw new Error(error.response?.data?.message || '獲取上傳憑證失敗');
  }
}

/**
 * 直接上傳檔案到 GCS
 */
export async function uploadFileToGCS(
  file: File,
  credential: UploadCredential,
  onProgress?: (progress: UploadProgress) => void
): Promise<void> {
  try {
    console.log('開始上傳檔案到 GCS:', {
      fileName: credential.originalName,
      fileSize: file.size,
      mimeType: credential.mimeType,
      signedUrl: credential.signedUrl.substring(0, 100) + '...'
    });

    // 使用 XMLHttpRequest 進行上傳，支援進度追蹤
    await new Promise<void>((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (onProgress && event.lengthComputable) {
          const progress: UploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100),
            fileName: credential.originalName
          };
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          console.error('GCS 上傳錯誤回應:', {
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText
          });
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('網路錯誤'));
      });

      xhr.addEventListener('timeout', () => {
        reject(new Error('上傳超時'));
      });

      xhr.open('PUT', credential.signedUrl);
      xhr.setRequestHeader('Content-Type', credential.mimeType);
      xhr.timeout = 300000; // 5 分鐘
      xhr.send(file);
    });

    console.log('檔案上傳成功:', credential.originalName);
  } catch (error: any) {
    console.error(`上傳檔案 ${credential.originalName} 失敗:`, error);
    console.error('錯誤詳情:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      headers: error.response?.headers,
      data: error.response?.data
    });
    throw new Error(`上傳檔案 ${credential.originalName} 失敗: ${error.message}`);
  }
}

/**
 * 驗證檔案上傳完成
 */
export async function verifyUploads(credentials: UploadCredential[]): Promise<UploadResult[]> {
  try {
    const files = credentials.map(cred => ({
      fileName: cred.fileName,
      publicUrl: cred.publicUrl,
      originalName: cred.originalName,
      mimeType: cred.mimeType,
      fileType: cred.fileType
    }));

    const response = await axios.post('/upload/verify', { files });
    return response.data.files;
  } catch (error: any) {
    console.error('驗證上傳失敗:', error);
    throw new Error(error.response?.data?.message || '驗證上傳失敗');
  }
}

/**
 * 完整的直接上傳流程
 */
export async function directUploadFiles(
  files: File[],
  fileType: 'image' | 'video' | 'avatar' | 'cover' | 'banner' = 'image',
  onProgress?: (overall: number, current: UploadProgress | null) => void
): Promise<UploadResult[]> {
  try {
    // 1. 準備檔案資訊
    const fileInfos: FileUploadInfo[] = files.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      fileType
    }));

    if (onProgress) {
      onProgress(0, null);
    }

    // 2. 獲取上傳憑證
    const credentials = await getUploadCredentials(fileInfos);

    if (onProgress) {
      onProgress(10, null);
    }

    // 3. 逐個上傳檔案
    const uploadPromises = credentials.map(async (credential, index) => {
      const file = files[index];
      
      await uploadFileToGCS(file, credential, (progress) => {
        if (onProgress) {
          const overallProgress = 10 + ((index + progress.percentage / 100) / files.length) * 80;
          onProgress(Math.round(overallProgress), progress);
        }
      });

      return credential;
    });

    await Promise.all(uploadPromises);

    if (onProgress) {
      onProgress(90, null);
    }

    // 4. 驗證上傳完成
    const results = await verifyUploads(credentials);

    if (onProgress) {
      onProgress(100, null);
    }

    return results;

  } catch (error) {
    console.error('直接上傳失敗:', error);
    throw error;
  }
}

/**
 * 批量上傳（支援不同檔案類型）
 */
export async function batchUploadFiles(
  uploads: { files: File[]; fileType: string }[],
  onProgress?: (overall: number, current: UploadProgress | null) => void
): Promise<UploadResult[]> {
  const allResults: UploadResult[] = [];
  const totalUploads = uploads.reduce((sum, upload) => sum + upload.files.length, 0);
  let completedUploads = 0;

  for (const upload of uploads) {
    const results = await directUploadFiles(
      upload.files,
      upload.fileType as any,
      (fileProgress, current) => {
        if (onProgress) {
          const overallProgress = ((completedUploads + fileProgress / 100) / totalUploads) * 100;
          onProgress(Math.round(overallProgress), current);
        }
      }
    );

    allResults.push(...results);
    completedUploads += upload.files.length;
  }

  return allResults;
}

/**
 * 獲取檔案上傳限制
 */
export async function getUploadLimits(): Promise<any> {
  try {
    const response = await axios.get('/upload/limits');
    return response.data.limits;
  } catch (error) {
    console.error('獲取上傳限制失敗:', error);
    throw new Error('獲取上傳限制失敗');
  }
}

/**
 * 檢查檔案是否符合上傳限制
 */
export function validateFile(
  file: File,
  fileType: string,
  limits: any
): { valid: boolean; error?: string } {
  const limit = limits[fileType];
  if (!limit) {
    return { valid: false, error: `不支援的檔案類型: ${fileType}` };
  }

  if (!limit.allowedTypes.includes(file.type)) {
    return { valid: false, error: `不支援的檔案格式: ${file.type}` };
  }

  if (file.size > limit.maxSize) {
    const maxSizeMB = Math.round(limit.maxSize / 1024 / 1024);
    const fileSizeMB = Math.round(file.size / 1024 / 1024);
    return { 
      valid: false, 
      error: `檔案大小超過限制: ${fileSizeMB}MB > ${maxSizeMB}MB` 
    };
  }

  return { valid: true };
}

/**
 * 格式化檔案大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
