import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from 'axios'
// Swiper CSS
import 'swiper/swiper-bundle.css'

// 根據環境設置 API 基礎路徑
const isDevelopment = import.meta.env.DEV;
if (isDevelopment) {
  // 開發環境：直接連接後端服務器
  axios.defaults.baseURL = 'http://*************:5003/api';
} else {
  // 生產環境：使用相對路徑，通過 Nginx 代理
  axios.defaults.baseURL = '/api';
}

// 解析網址 ?jwt=xxx 參數，若存在則儲存至 localStorage 並清除網址參數
const urlParams = new URLSearchParams(window.location.search);
const jwtFromUrlRaw = urlParams.get('jwt');
const jwtFromUrl = jwtFromUrlRaw ? decodeURIComponent(jwtFromUrlRaw) : null;
if (jwtFromUrl && jwtFromUrl !== 'undefined' && jwtFromUrl !== 'null') {
  const cleaned = jwtFromUrl.trim().replace(/\s+/g, '');
  // 清除舊帳號的本地喜歡/評論快取
  localStorage.removeItem('likedPosts');
  localStorage.removeItem('likedComments');
  localStorage.setItem('jwt', cleaned);
  urlParams.delete('jwt');
  const newQuery = urlParams.toString();
  const newUrl = window.location.pathname + (newQuery ? `?${newQuery}` : '') + window.location.hash;
  window.history.replaceState({}, '', newUrl);
}

// 從 localStorage 讀取 JWT 並注入 axios 預設標頭
const savedJwtRaw = localStorage.getItem('jwt');
const savedJwt = savedJwtRaw ? savedJwtRaw.trim().replace(/\s+/g, '') : null;
if (savedJwt && savedJwt !== 'undefined' && savedJwt !== 'null') {
  axios.defaults.headers.common['Authorization'] = `Bearer ${savedJwt}`;

  // 取得當前使用者資訊並存 userId
  axios.get('/auth/me')
    .then(res => {
      localStorage.setItem('userId', res.data.user.id);
    })
    .catch(() => {
      localStorage.removeItem('userId');
    });
}

// 登出幫手 (可在組件中 import)
export function logout() {
  localStorage.removeItem('jwt');
  localStorage.removeItem('likedPosts');
  localStorage.removeItem('likedComments');
  delete axios.defaults.headers.common['Authorization'];
  window.location.reload();
}

createApp(App).use(router).mount('#app')
