const { Storage } = require('@google-cloud/storage');
const path = require('path');
require('dotenv').config();

// 初始化 Google Cloud Storage
const keyFilePath = process.env.GCP_KEY_FILE
  ? (path.isAbsolute(process.env.GCP_KEY_FILE)
     ? process.env.GCP_KEY_FILE
     : path.join(__dirname, process.env.GCP_KEY_FILE))
  : path.join(__dirname, 'config/ccw-dev-81e940b67f37.json');

const storage = new Storage({
  keyFilename: keyFilePath,
  projectId: process.env.GCP_PROJECT_ID || 'ccw-dev',
});

const bucketName = process.env.GCP_BUCKET_NAME || 'liveshowsotime';

async function setupCORS() {
  try {
    const bucket = storage.bucket(bucketName);
    
    // CORS 設定
    const corsConfiguration = [
      {
        origin: [
          'https://liveshow.sotime.app',
          'http://localhost:5173',
          'http://localhost:6666',
          '*'  // 暫時允許所有來源進行測試
        ],
        method: [
          'GET',
          'HEAD',
          'PUT',
          'POST',
          'DELETE',
          'OPTIONS'
        ],
        responseHeader: [
          'Content-Type',
          'Content-Length',
          'Content-Range',
          'Access-Control-Allow-Origin',
          'Access-Control-Allow-Methods',
          'Access-Control-Allow-Headers',
          'Access-Control-Allow-Credentials',
          'Access-Control-Max-Age',
          'ETag',
          'x-goog-*'
        ],
        maxAgeSeconds: 3600
      }
    ];

    console.log('設定 CORS 政策...');
    await bucket.setCorsConfiguration(corsConfiguration);
    console.log('✅ CORS 政策設定成功！');
    
    // 驗證設定
    console.log('驗證 CORS 設定...');
    const [metadata] = await bucket.getMetadata();
    console.log('當前 CORS 設定:', JSON.stringify(metadata.cors, null, 2));
    
  } catch (error) {
    console.error('❌ 設定 CORS 失敗:', error);
    process.exit(1);
  }
}

// 執行設定
setupCORS();
