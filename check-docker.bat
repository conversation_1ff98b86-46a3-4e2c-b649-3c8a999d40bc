@echo off
echo 檢查 Docker 環境...

REM 檢查 Docker Desktop 是否運行
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Desktop 未運行或未安裝
    echo.
    echo 請執行以下步驟:
    echo 1. 確保已安裝 Docker Desktop for Windows
    echo 2. 啟動 Docker Desktop 應用程序
    echo 3. 等待 Docker 完全啟動 ^(狀態欄圖標變為綠色^)
    echo 4. 重新運行此腳本
    echo.
    echo 下載地址: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

echo [SUCCESS] Docker 運行正常
echo.

REM 顯示 Docker 版本信息
echo Docker 版本信息:
docker --version
docker-compose --version
echo.

REM 檢查 Docker 資源
echo Docker 系統信息:
docker system df
echo.

echo [SUCCESS] Docker 環境檢查完成！
echo.
echo 現在您可以運行:
echo   deploy.bat prod start
echo.
pause
