const { Storage } = require('@google-cloud/storage');
const path = require('path');
require('dotenv').config();

// 初始化 Google Cloud Storage
const keyFilePath = process.env.GCP_KEY_FILE
  ? (path.isAbsolute(process.env.GCP_KEY_FILE)
     ? process.env.GCP_KEY_FILE
     : path.join(__dirname, process.env.GCP_KEY_FILE))
  : path.join(__dirname, 'config/ccw-dev-81e940b67f37.json');

const storage = new Storage({
  keyFilename: keyFilePath,
  projectId: process.env.GCP_PROJECT_ID || 'ccw-dev',
});

const bucketName = process.env.GCP_BUCKET_NAME || 'liveshowsotime';

async function forceCORSSetup() {
  try {
    const bucket = storage.bucket(bucketName);
    
    console.log('🔧 強制設定 CORS 政策...');
    
    // 最寬鬆的 CORS 設定
    const corsConfiguration = [
      {
        origin: ['*'],
        method: ['*'],
        responseHeader: ['*'],
        maxAgeSeconds: 3600
      }
    ];

    // 設定 CORS
    await bucket.setCorsConfiguration(corsConfiguration);
    console.log('✅ CORS 政策設定完成！');
    
    // 等待一下讓設定生效
    console.log('⏳ 等待 5 秒讓設定生效...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 驗證設定
    console.log('🔍 驗證 CORS 設定...');
    const [metadata] = await bucket.getMetadata();
    console.log('當前 CORS 設定:', JSON.stringify(metadata.cors, null, 2));
    
    // 測試 CORS
    console.log('🧪 測試 CORS 功能...');
    await testCORS();
    
  } catch (error) {
    console.error('❌ 設定 CORS 失敗:', error);
    
    // 嘗試替代方法
    console.log('🔄 嘗試替代方法...');
    await alternativeCORSSetup();
  }
}

async function testCORS() {
  try {
    // 創建一個測試檔案
    const bucket = storage.bucket(bucketName);
    const testFile = bucket.file('cors-test.txt');
    
    // 生成 Signed URL
    const [signedUrl] = await testFile.getSignedUrl({
      version: 'v4',
      action: 'write',
      expires: Date.now() + 15 * 60 * 1000,
      contentType: 'text/plain',
    });
    
    console.log('📝 生成的測試 Signed URL:', signedUrl);
    
    // 使用 fetch 測試 CORS
    const { default: fetch } = await import('node-fetch');
    
    const response = await fetch(signedUrl, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://liveshow.sotime.app',
        'Access-Control-Request-Method': 'PUT',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    console.log('🔍 CORS 測試結果:');
    console.log('狀態碼:', response.status);
    console.log('回應標頭:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok || response.status === 200) {
      console.log('✅ CORS 測試成功！');
    } else {
      console.log('❌ CORS 測試失敗');
    }
    
  } catch (error) {
    console.log('⚠️ CORS 測試錯誤:', error.message);
  }
}

async function alternativeCORSSetup() {
  try {
    console.log('🔄 使用替代方法設定 CORS...');
    
    const bucket = storage.bucket(bucketName);
    
    // 嘗試更簡單的 CORS 設定
    const simpleCors = [
      {
        origin: ['*'],
        method: ['GET', 'PUT', 'POST', 'HEAD', 'DELETE', 'OPTIONS'],
        responseHeader: ['Content-Type', 'Access-Control-Allow-Origin', 'Access-Control-Allow-Methods', 'Access-Control-Allow-Headers'],
        maxAgeSeconds: 86400
      }
    ];
    
    await bucket.setCorsConfiguration(simpleCors);
    console.log('✅ 替代 CORS 設定完成！');
    
    // 驗證
    const [metadata] = await bucket.getMetadata();
    console.log('新的 CORS 設定:', JSON.stringify(metadata.cors, null, 2));
    
  } catch (error) {
    console.error('❌ 替代方法也失敗:', error);
    
    // 提供手動設定指令
    console.log('\n📋 請手動執行以下命令：');
    console.log(`gsutil cors set gcs-cors-config.json gs://${bucketName}`);
    console.log('或者：');
    console.log(`echo '[{"origin":["*"],"method":["*"],"responseHeader":["*"],"maxAgeSeconds":3600}]' | gsutil cors set /dev/stdin gs://${bucketName}`);
  }
}

// 執行強制設定
forceCORSSetup();
