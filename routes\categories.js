const express = require('express');
const router = express.Router();
const Category = require('../models/Category');

// 取得所有啟用中的分類
router.get('/', async (req, res) => {
  try {
    const categories = await Category.find({ isActive: true, slug: { $ne: 'all' } }).sort({ order: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得分類失敗' });
  }
});

module.exports = router; 