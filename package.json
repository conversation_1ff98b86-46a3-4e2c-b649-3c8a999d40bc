{"name": "wall-server", "version": "1.0.0", "description": "貼文牆 Node.js + MongoDB backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm run dev", "dev:all": "concurrently \"npm run dev\" \"npm run client\"", "install:all": "npm install && cd client && npm install", "seed:livedata": "node scripts/seedLiveData.js"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@google-cloud/storage": "^7.16.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1"}}