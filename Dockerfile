# 多階段構建 Dockerfile
# 階段 1: 構建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app/client

# 複製前端 package.json 和 package-lock.json
COPY client/package*.json ./

# 安裝前端依賴（包含開發依賴，用於構建）
RUN npm install

# 複製前端源碼
COPY client/ ./

# 構建前端應用
RUN npm run build

# 階段 2: 構建後端
FROM node:18-alpine AS backend-builder

WORKDIR /app

# 複製後端 package.json 和 package-lock.json
COPY package*.json ./

# 安裝後端依賴
RUN npm install --omit=dev

# 複製後端源碼
COPY . ./

# 移除客戶端目錄（已在前端階段處理）
RUN rm -rf client

# 階段 3: Nginx + Node.js 運行環境
FROM nginx:alpine AS production

# 安裝 Node.js
RUN apk add --no-cache nodejs npm

# 創建應用目錄
WORKDIR /app

# 從後端構建階段複製應用
COPY --from=backend-builder /app ./

# 從前端構建階段複製構建產物
COPY --from=frontend-builder /app/client/dist ./public

# 複製 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 創建啟動腳本
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'nginx &' >> /start.sh && \
    echo 'cd /app && node server.js' >> /start.sh && \
    chmod +x /start.sh

# 暴露端口
EXPOSE 6666

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:6666/api/health || exit 1

# 啟動命令
CMD ["/start.sh"]
