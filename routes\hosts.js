const express = require('express');
const router = express.Router();
const protect = require('../middleware/auth');
const User = require('../models/User');

// 取得目前登入使用者追蹤的主播清單
router.get('/following', protect, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate('following', 'name avatar');
    res.json(user.following || []);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得追蹤主播失敗' });
  }
});

module.exports = router; 