// MongoDB 初始化腳本
db = db.getSiblingDB('poster');

// 創建應用用戶
db.createUser({
  user: 'hoolyhi',
  pwd: 'Sayhong168888',
  roles: [
    {
      role: 'readWrite',
      db: 'poster'
    }
  ]
});

// 創建索引以提升性能
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.posts.createIndex({ "createdAt": -1 });
db.posts.createIndex({ "author": 1 });
db.liverooms.createIndex({ "category": 1 });
db.liverooms.createIndex({ "isLive": 1 });
db.rankings.createIndex({ "type": 1, "period": 1 }, { unique: true });

print('Database initialized successfully!');
