<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->

<template>
  <div class="app-container bg-gray-100 min-h-screen text-left">
    <!-- 頂部導航欄 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200">
      <div class="flex justify-between items-center px-4 h-14">
        <div class="text-xl font-bold text-blue-600">社群牆</div>
        <div class="flex items-center space-x-4">
          <button @click="openPostCreation" class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white cursor-pointer">
            <i class="fas fa-plus"></i>
          </button>
          <div class="w-8 h-8 rounded-full overflow-hidden cursor-pointer">
            <img :src="userAvatar" alt="用戶頭像" class="w-full h-full object-cover" @error="handleAvatarError" />
          </div>
        </div>
      </div>
    </header>

    <!-- 主內容區域 -->
    <main class="pt-16 pb-16">
      <!-- 貼文牆頁面 -->
      <div v-if="!isPostCreationOpen" class="post-feed px-4">
        <div v-if="isLoading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <div v-for="post in posts" :key="post._id" class="post-card bg-white rounded-2xl shadow-md mb-4 overflow-hidden w-[94%] mx-auto">
          <!-- 用戶資訊區 -->
          <div class="flex items-center p-4">
            <div class="w-10 h-10 rounded-full overflow-hidden mr-3">
              <img :src="post.author.avatar" alt="用戶頭像" class="w-full h-full object-cover" @error="handleAvatarError" />
            </div>
            <div class="flex-1">
              <div class="font-bold text-gray-800">{{ post.author.name }}</div>
              <div class="text-xs text-gray-500">{{ formatTime(post.createdAt) }}</div>
            </div>
            <!-- 添加操作菜单 -->
            <div v-if="isPostOwner(post)" class="relative">
              <button @click.stop="togglePostMenu(post)" class="p-1 bg-transparent">
                <i class="fas fa-ellipsis-v text-gray-500"></i>
              </button>
              <div v-if="post.showMenu" @click.stop class="absolute right-0 mt-1 bg-white rounded-lg shadow-md p-2 z-20 w-24">
                <button @click="openPostEdit(post)" class="w-full text-left py-1 px-2 hover:bg-gray-100 rounded text-sm">
                  <i class="fas fa-edit mr-2"></i>編輯
                </button>
                <button @click="confirmDeletePost(post)" class="w-full text-left py-1 px-2 hover:bg-gray-100 rounded text-sm text-red-500">
                  <i class="fas fa-trash-alt mr-2"></i>刪除
                </button>
              </div>
            </div>
          </div>

          <!-- 內容區 -->
          <div class="px-4 pb-2">
            <p :class="{ 'line-clamp-3': !post.expanded, 'cursor-pointer': post.content.length > 150 }"
               @click="post.content.length > 150 && toggleExpand(post)"
               class="text-gray-800 mb-2">
              {{ post.content }}
              <span v-if="post.isEdited" class="text-xs text-gray-500 ml-1">(已編輯)</span>
            </p>
          </div>

          <!-- 媒體區 -->
          <div v-if="post.media && post.media.length > 0" class="media-container">
            <!-- 單張 -->
            <div v-if="post.media.length === 1" class="single-media">
              <img v-if="post.media[0].type === 'image'" :src="post.media[0].url" alt="貼文圖片" class="w-full h-auto object-top rounded-lg cursor-pointer" @click="openMediaViewer(post.media, 0)" />
              <video v-else-if="post.media[0].type === 'video'"
                     playsinline
                     muted
                     loop
                     :ref="el => videoElements[`${post._id}_0`] = el"
                     class="video-js vjs-big-play-centered w-full rounded-lg cursor-pointer"
                     preload="metadata"
                     data-setup='{"controls": false, "autoplay": "muted", "loop": true}'
                     @click="toggleVideoPlayback(`${post._id}_0`)">
                <source :src="post.media[0].url" :type="post.media[0].mimeType" />
                您的瀏覽器不支援影片播放。
              </video>
            </div>
            <!-- 多張 -->
            <div v-else class="grid grid-cols-2 gap-1">
              <div v-for="(media, index) in post.media.slice(0, 4)" :key="index" class="media-item relative h-40">
                <img v-if="media.type === 'image'" :src="media.url" alt="貼文圖片" class="w-full h-full object-cover object-top rounded-md cursor-pointer" @click="openMediaViewer(post.media, index)" />
                <video v-else-if="media.type === 'video'"
                       playsinline
                       muted
                       loop
                       :ref="el => videoElements[`${post._id}_${index}`] = el"
                       class="video-js vjs-big-play-centered w-full h-full object-cover rounded-md cursor-pointer"
                       preload="metadata"
                       data-setup='{"controls": false, "autoplay": "muted", "loop": true}'
                       @click="toggleVideoPlayback(`${post._id}_${index}`)">
                  <source :src="media.url" :type="media.mimeType" />
                </video>
                <div v-if="index === 3 && post.media.length > 4" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-xl font-bold rounded-md cursor-pointer" @click="openMediaViewer(post.media, 4)">
                  +{{ post.media.length - 4 }}
                </div>
              </div>
            </div>
          </div>

          <!-- 互動區 -->
          <div class="px-4 py-3 border-t border-gray-100">
            <div class="flex justify-between text-sm text-gray-500 mb-2">
              <div @click="openLikesDialog(post)" class="cursor-pointer hover:text-blue-600">{{ post.likes }} 人喜歡</div>
              <div @click="toggleComments(post)" class="cursor-pointer hover:text-blue-600">{{ post.comments.length }} 則評論</div>
            </div>
            <div class="flex justify-between border-t border-gray-100 pt-2">
              <button @click="toggleLike(post)"
                :class="['flex-1 flex items-center justify-center py-2 px-0 bg-transparent hover:bg-gray-100', post.isLiked ? 'text-blue-600' : 'text-gray-500']">
                <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'" class="mr-2"></i> 喜歡
              </button>
              <button @click="toggleComments(post)" class="flex-1 flex items-center justify-center py-2 px-0 bg-transparent hover:bg-gray-100 text-gray-500">
                <i class="far fa-comment mr-2"></i> 評論
              </button>
            </div>
          </div>

          <!-- 評論區 -->
          <div v-if="post.showComments" class="comments-section bg-gray-50 p-4 border-t border-gray-100">
            <div v-for="comment in post.comments" :key="comment._id" class="comment mb-3">
              <div class="flex">
                <div class="w-8 h-8 rounded-full overflow-hidden mr-2">
                  <img :src="comment.author.avatar" alt="評論者頭像" class="w-full h-full object-cover" @error="handleAvatarError" />
                </div>
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 flex-1">
                  <div class="font-medium text-sm text-gray-600">{{ comment.author.name }}</div>
                  <div class="text-sm text-gray-900">
                    {{ comment.content }}
                    <span v-if="comment.isEdited" class="text-xs text-gray-500 ml-1">(已編輯)</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ formatTime(comment.createdAt) }}</div>
                  <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                    <div>
                      <button @click="toggleCommentLike(post, comment)" :class="{ 'text-blue-600': comment.isLiked }" class="mr-1 focus:outline-none bg-transparent">
                        <i :class="comment.isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
                      </button>
                      <span>{{ comment.likes > 0 ? comment.likes : '' }}</span>
                    </div>
                    <div v-if="isCommentOwner(comment)" class="relative">
                      <button @click.stop="toggleCommentMenu(post, comment)" class="p-1 bg-transparent">
                        <i class="fas fa-ellipsis-h"></i>
                      </button>
                      <div v-if="comment.showMenu" @click.stop class="absolute right-0 mt-1 bg-white rounded-lg shadow-md p-2 z-20 w-24">
                        <button @click="openCommentEdit(post, comment)" class="w-full text-left py-1 px-2 hover:bg-gray-100 rounded text-xs">
                          <i class="fas fa-edit mr-2"></i>編輯
                        </button>
                        <button @click="confirmDeleteComment(post, comment)" class="w-full text-left py-1 px-2 hover:bg-gray-100 rounded text-xs text-red-500">
                          <i class="fas fa-trash-alt mr-2"></i>刪除
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 評論輸入框 -->
            <div class="flex items-center mt-2">
              <div class="w-8 h-8 rounded-full overflow-hidden mr-2">
                <img :src="userAvatar" alt="用戶頭像" class="w-full h-full object-cover" @error="handleAvatarError" />
              </div>
              <div class="flex-1 bg-gray-50 rounded-full border border-gray-200 overflow-hidden">
                <input v-model="post.newComment" type="text" placeholder="寫下你的評論..." class="w-full px-4 py-2 text-sm border-none focus:outline-none bg-gray-50 text-gray-900 placeholder-gray-400" @keyup.enter="addComment(post)" />
              </div>
              <button @click="addComment(post)" class="ml-2 bg-blue-600 text-white p-2 rounded-full flex items-center justify-center">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>

        <div v-if="!isLoading && posts.length === 0" class="flex flex-col items-center justify-center py-10">
          <i class="far fa-comment-dots text-5xl text-gray-300 mb-4"></i>
          <p class="text-gray-500">目前還沒有貼文</p>
          <p class="text-gray-400 text-sm">成為第一個發文的人吧！</p>
        </div>
      </div>

      <!-- 張貼文章頁面 -->
      <div v-else class="post-creation fixed inset-0 bg-white z-50 flex flex-col">
        <!-- 頂部操作欄 -->
        <div class="flex justify-between items-center px-4 h-14 border-b border-gray-200">
          <button @click="closePostCreation" class="text-gray-600 cursor-pointer bg-transparent">取消</button>
          <div class="font-medium">新增貼文</div>
          <button
            @click="createPost"
            :disabled="!canPublish"
            :class="{ 'opacity-50': !canPublish, 'cursor-not-allowed': !canPublish }"
            class="text-blue-600 font-medium bg-transparent"
            :style="{ cursor: canPublish ? 'pointer' : 'not-allowed' }"
          >
            發布
          </button>
        </div>

        <!-- 內容編輯區 -->
        <div class="flex-1 overflow-y-auto p-4">
          <textarea v-model="newPostContent" placeholder="分享你的想法..." class="w-full bg-gray-50 border border-gray-200 rounded-lg p-3 resize-none focus:outline-none text-base mb-4 text-gray-900 placeholder-gray-400" rows="5"></textarea>

          <!-- 壓縮進度顯示 -->
          <div v-if="isCompressing" class="compression-progress mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center mb-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span class="text-sm text-blue-800">正在壓縮檔案...</span>
            </div>
            <div v-if="compressionProgress" class="text-xs text-blue-600">
              <div class="mb-1">{{ compressionProgress.currentFile }}</div>
              <div class="w-full bg-blue-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: compressionProgress.progress + '%' }"></div>
              </div>
              <div class="mt-1">{{ compressionProgress.current }}/{{ compressionProgress.total }} ({{ compressionProgress.progress }}%)</div>
            </div>
          </div>

          <!-- 上傳進度顯示 -->
          <div v-if="isUploading" class="upload-progress mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center mb-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
              <span class="text-sm text-green-800">正在上傳檔案...</span>
            </div>
            <div class="text-xs text-green-600">
              <div v-if="uploadProgress.current" class="mb-1">{{ uploadProgress.current.fileName }}</div>
              <div class="w-full bg-green-200 rounded-full h-2">
                <div class="bg-green-600 h-2 rounded-full transition-all duration-300" :style="{ width: uploadProgress.overall + '%' }"></div>
              </div>
              <div class="mt-1">總進度: {{ uploadProgress.overall }}%</div>
              <div v-if="uploadProgress.current" class="text-xs">
                當前檔案: {{ uploadProgress.current.percentage }}% ({{ formatFileSize(uploadProgress.current.loaded) }}/{{ formatFileSize(uploadProgress.current.total) }})
              </div>
            </div>
          </div>

          <!-- 媒體預覽區 -->
          <div v-if="selectedMedia.length > 0" class="media-preview mb-4">
            <!-- 單檔 -->
            <div v-if="selectedMedia.length === 1" class="single-preview relative">
              <img v-if="selectedMedia[0].type === 'image'" :src="selectedMedia[0].preview" alt="預覽圖片" class="w-full h-auto rounded-lg" />
              <video v-else-if="selectedMedia[0].type === 'video'" playsinline controls class="w-full h-auto rounded-lg">
                <source :src="selectedMedia[0].preview" :type="selectedMedia[0].file.type" />
              </video>
              <!-- 壓縮資訊 -->
              <div v-if="selectedMedia[0].compressionRatio && selectedMedia[0].compressionRatio > 0" class="absolute bottom-2 left-2 bg-green-600 text-white text-sm px-3 py-1 rounded">
                已壓縮 {{ selectedMedia[0].compressionRatio }}%
                <span class="text-xs">({{ formatFileSize(selectedMedia[0].originalSize || 0) }} → {{ formatFileSize(selectedMedia[0].compressedSize || 0) }})</span>
              </div>
              <button @click="removeMedia(0)" class="absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white cursor-pointer">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <!-- 多檔 -->
            <div v-else class="grid grid-cols-2 gap-2">
              <div v-for="(media, index) in selectedMedia" :key="index" class="preview-item relative">
                <img v-if="media.type === 'image'" :src="media.preview" alt="預覽圖片" class="w-full h-40 object-cover rounded-lg" />
                <video v-else-if="media.type === 'video'" playsinline class="w-full h-40 object-cover rounded-lg">
                  <source :src="media.preview" :type="media.file.type" />
                </video>
                <!-- 壓縮資訊 -->
                <div v-if="media.compressionRatio && media.compressionRatio > 0" class="absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                  壓縮 {{ media.compressionRatio }}%
                </div>
                <button @click="removeMedia(index)" class="absolute top-2 right-2 w-6 h-6 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white cursor-pointer">
                  <i class="fas fa-times text-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部功能區 -->
        <div class="border-t border-gray-200 p-4 flex items-center justify-between">
          <div class="flex space-x-4">
            <label class="cursor-pointer">
              <i class="fas fa-camera text-xl text-gray-600"></i>
              <input type="file" accept="image/*" capture="camera" multiple class="hidden" @change="handleMediaSelect" />
            </label>
            <label class="cursor-pointer">
              <i class="fas fa-images text-xl text-gray-600"></i>
              <input type="file" accept="image/*,video/*" multiple class="hidden" @change="handleMediaSelect" />
            </label>
          </div>
          <div class="text-sm text-gray-500">
            <div v-if="selectedMedia.length > 0">已選擇 {{ selectedMedia.length }} 個檔案</div>
            <div v-if="compressionResults.length > 0" class="text-xs text-green-600 mt-1">
              總共節省 {{ formatFileSize(compressionResults.reduce((sum, r) => sum + (r.originalSize - r.compressedSize), 0)) }}
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 編輯貼文對話框 -->
    <div v-if="editPostDialog.show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg w-full max-w-md mx-4 max-h-[90vh] flex flex-col">
        <div class="p-4 overflow-y-auto flex-grow">
          <div class="text-lg font-medium mb-4">編輯貼文</div>
          <textarea v-model="editPostDialog.content" class="w-full bg-gray-50 border border-gray-200 rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 placeholder-gray-400" rows="5"></textarea>
          <!-- 媒體編輯區 -->
          <div class="mt-4">
            <div class="font-medium mb-2">媒體編輯</div>
            <div class="grid grid-cols-3 gap-2 mb-2">
              <div v-for="(media, idx) in editMedia" :key="media._id" class="relative">
                <img v-if="media.type==='image'" :src="media.url" class="w-full h-24 object-cover rounded" />
                <video v-else controls playsinline class="w-full h-24 rounded"><source :src="media.url" :type="media.mimeType" /></video>
                <button @click="removeEditMedia(media, idx)" class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full p-1"><i class="fas fa-times"></i></button>
              </div>
              <div v-for="(media, idx) in editNewMedia" :key="media.preview" class="relative">
                <img v-if="media.type==='image'" :src="media.preview" class="w-full h-24 object-cover rounded" />
                <video v-else controls playsinline class="w-full h-24 rounded"><source :src="media.preview" :type="media.mimeType" /></video>
                <button @click="removeNewEditMedia(idx)" class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full p-1"><i class="fas fa-times"></i></button>
              </div>
            </div>
            <label class="cursor-pointer text-blue-600 flex items-center">
              <i class="fas fa-images mr-1"></i>添加媒體
              <input type="file" accept="image/*,video/*" multiple class="hidden" @change="handleEditMediaSelect" />
            </label>
          </div>
        </div>
        <div class="border-t p-4 flex justify-end gap-3 flex-shrink-0 bg-white">
          <button @click="closeEditPostDialog" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">取消</button>
          <button @click="updatePost" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">儲存</button>
        </div>
      </div>
    </div>

    <!-- 編輯評論對話框 -->
    <div v-if="editCommentDialog.show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-4 w-full max-w-md mx-4">
        <div class="text-lg font-medium mb-4">編輯評論</div>
        <textarea v-model="editCommentDialog.content" class="w-full bg-gray-50 border border-gray-200 rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 placeholder-gray-400" rows="3"></textarea>
        <div class="flex justify-end mt-4 space-x-3">
          <button @click="closeEditCommentDialog" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">取消</button>
          <button @click="updateComment" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">儲存</button>
        </div>
      </div>
    </div>

    <!-- 確認刪除對話框 -->
    <div v-if="deleteDialog.show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-4 w-full max-w-md mx-4">
        <div class="text-lg font-medium mb-2">確認刪除</div>
        <p class="text-gray-600 mb-4">{{ deleteDialog.message }}</p>
        <div class="flex justify-end space-x-3">
          <button @click="closeDeleteDialog" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">取消</button>
          <button @click="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">刪除</button>
        </div>
      </div>
    </div>

    <!-- 按讚名單對話框 -->
    <div v-if="likesDialog.show" @click="closeLikesDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div @click.stop class="bg-white rounded-lg p-4 w-80 max-h-[70vh] overflow-y-auto relative">
        <button @click="closeLikesDialog" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 bg-transparent">
          <i class="fas fa-times"></i>
        </button>
        <div class="text-lg font-medium mb-4">按讚名單</div>
        <ul>
          <li v-for="user in likesDialog.users" :key="user._id" class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-full overflow-hidden mr-2">
              <img :src="user.avatar" alt="頭像" class="w-full h-full object-cover" @error="handleAvatarError" />
            </div>
            <div class="text-sm text-gray-800">{{ user.name }}</div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 載入中遮罩 -->
    <div v-if="isPublishing" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-64 flex flex-col items-center">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p class="text-gray-700">正在發布貼文...</p>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="showSuccessToast" class="fixed top-16 left-0 right-0 flex justify-center z-50">
      <div class="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">貼文發布成功！</div>
    </div>

    <!-- API 錯誤提示 -->
    <div v-if="apiError" class="fixed top-16 left-0 right-0 flex justify-center z-50">
      <div class="bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg">
        出錯啦：{{ apiError }}
      </div>
    </div>

    <!-- 媒體檢視器 -->
    <div v-if="mediaViewer.show" @click="closeMediaViewer" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex flex-col">
      <!-- 頂部控制列 -->
      <div class="p-4 flex justify-between items-center text-white" @click.stop>
        <div class="text-sm">{{ mediaViewer.currentIndex + 1 }} / {{ mediaViewer.media.length }}</div>
        <button @click="closeMediaViewer" class="text-2xl bg-transparent border-0 outline-none focus:outline-none">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <!-- 媒體顯示區 -->
      <div class="flex-1 flex items-center justify-center overflow-hidden" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
        <div class="swiper-container w-full h-full">
          <div 
            class="swiper-wrapper flex h-full transition-transform duration-300 ease-in-out"
            :style="{ transform: `translateX(-${mediaViewer.currentIndex * 100}%)` }"
          >
            <template v-for="(media, index) in mediaViewer.media" :key="index">
              <div class="swiper-slide w-full h-full flex-shrink-0 flex items-center justify-center">
                <img v-if="media.type === 'image'" :src="media.url" alt="媒體檢視" class="w-full max-h-full object-contain" @click.stop />
                <video v-else-if="media.type === 'video'"
                       playsinline
                       muted
                       loop
                       autoplay
                       class="w-full max-h-full video-js vjs-big-play-centered"
                       data-setup='{"controls": true, "autoplay": "muted", "loop": true}'>
                  <source :src="media.url" :type="media.mimeType" />
                  您的瀏覽器不支援影片播放。
                </video>
              </div>
            </template>
          </div>
        </div>
        <!-- 導航按鈕 (保持在 swiper-container 之外，但在媒體顯示區內) -->
        <button 
          v-if="mediaViewer.media.length > 1 && mediaViewer.currentIndex > 0" 
          @click.stop="navigateMedia(-1)" 
          class="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center text-white"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <button 
          v-if="mediaViewer.media.length > 1 && mediaViewer.currentIndex < mediaViewer.media.length - 1" 
          @click.stop="navigateMedia(1)" 
          class="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center text-white"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      
      <!-- 縮圖列 -->
      <div v-if="mediaViewer.media.length > 1" class="bg-black bg-opacity-50 p-2 overflow-x-auto" @click.stop>
        <div class="flex space-x-2">
          <div 
            v-for="(media, index) in mediaViewer.media" 
            :key="index" 
            @click="mediaViewer.currentIndex = index" 
            class="w-16 h-16 flex-shrink-0 cursor-pointer rounded-md overflow-hidden"
            :class="{ 'ring-2 ring-blue-500': index === mediaViewer.currentIndex }"
          >
            <img v-if="media.type === 'image'" :src="media.url" alt="縮圖" class="w-full h-full object-cover" />
            <div v-else-if="media.type === 'video'" class="w-full h-full bg-gray-900 flex items-center justify-center">
              <i class="fas fa-play text-white"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import axios from 'axios';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import { throttle } from 'lodash-es';
import { compressFiles, formatFileSize, type FileCompressionResult, type CompressionProgress } from '../utils/fileCompression';
import { directUploadFiles, getUploadLimits, validateFile, type UploadResult, type UploadProgress } from '../utils/directUpload';

// 用戶資訊（測試帳戶與後端一致）
const userAvatar = ref('https://readdy.ai/api/search-image?query=Asian%20young%20woman%20portrait&width=200&height=200');
const defaultAvatar = ref('https://ui-avatars.com/api/?name=User&background=random');

// 狀態
const isLoading = ref(true);
const isPostCreationOpen = ref(false);
const isPublishing = ref(false);
const showSuccessToast = ref(false);
const apiError = ref<string | null>(null);

// 分頁游標參數
const limit = ref<number>(20);
const lastId = ref<string | null>(null);

// 編輯與刪除對話框狀態
const editPostDialog = ref({ show: false, postId: '', content: '' });
const editCommentDialog = ref({ show: false, postId: '', commentId: '', content: '' });
const deleteDialog = ref({ show: false, type: '', postId: '', commentId: '', message: '' });
const likesDialog = ref<{ show: boolean; users: Author[] }>({ show: false, users: [] });

// 編輯貼文媒體狀態
const editMedia = ref<Media[]>([]);
const editNewMedia = ref<Media[]>([]);
const mediaToRemove = ref<string[]>([]);

// 媒體檢視器狀態
const mediaViewer = ref({
  show: false,
  media: [] as Media[],
  currentIndex: 0
});

// 新增：手勢支持狀態
const touchStartX = ref(0);
const touchStartY = ref(0);
const touchDirectionSet = ref(false);

/* --------- 用戶互動狀態：首個點擊/觸控後解除影片靜音 --------- */
const hasUserInteracted = ref(false);

// 檢測網絡狀況和設備性能
const networkInfo = ref({
  effectiveType: '4g', // 預設為4g
  saveData: false
});

// 檢測網絡狀況
function detectNetworkConditions() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    networkInfo.value = {
      effectiveType: connection.effectiveType || '4g',
      saveData: connection.saveData || false
    };
    console.log('Network conditions:', networkInfo.value);
  }
}

function handleFirstInteraction() {
  if (hasUserInteracted.value) return;
  hasUserInteracted.value = true;

  // 將所有已初始化的播放器解除靜音
  Object.values(videoPlayers.value).forEach(player => {
    if (player && !player.isDisposed()) {
      player.muted(false);
      // 若音量為 0，設為 1 以確保有聲音
      if (player.volume() === 0) {
        player.volume(1);
      }
    }
  });
}

// 新增：追蹤 video 元素和播放器實例
const videoElements = ref<Record<string, HTMLVideoElement | null>>({});
const videoPlayers = ref<Record<string, ReturnType<typeof videojs>>>({});

// 貼文類型
interface Author {
  _id: string;
  name: string;
  avatar: string;
}
interface Comment {
  _id: string;
  author: Author;
  content: string;
  createdAt: string;
  likes: number;
  isLiked: boolean;
  isEdited?: boolean;
  showMenu?: boolean;
}
interface Media {
  _id?: string;
  type: 'image' | 'video';
  url: string;
  mimeType?: string;
  preview?: string;
  file?: File;
  originalSize?: number;
  compressedSize?: number;
  compressionRatio?: number;
}
interface Post {
  _id: string;
  author: Author;
  content: string;
  media: Media[];
  likes: number;
  comments: Comment[];
  createdAt: string;
  expanded: boolean;
  showComments: boolean;
  isLiked: boolean;
  newComment: string;
  isEdited?: boolean;
  showMenu?: boolean;
}

const posts = ref<Post[]>([]);

// 新增：從後端獲取當前用戶頭像
async function fetchCurrentUser() {
  try {
    const { data } = await axios.get('/auth/me');
    userAvatar.value = data.user.avatar || defaultAvatar.value;
  } catch (error) {
    console.error('獲取用戶信息失敗：', error);
  }
}

// 初始化 Video.js 播放器
function initializeVideoPlayers() {
  nextTick(() => {
    // 清理不再存在的播放器
    const currentPlayerKeys = Object.keys(videoElements.value);
    Object.keys(videoPlayers.value).forEach(key => {
      if (!currentPlayerKeys.includes(key) || !videoElements.value[key]) {
        videoPlayers.value[key]?.dispose();
        delete videoPlayers.value[key];
      }
    });

    // 初始化新的播放器
    Object.entries(videoElements.value).forEach(([key, element]) => {
      if (element && !videoPlayers.value[key]) {
        // 根據網絡狀況調整設置
        const shouldAutoplay = !networkInfo.value.saveData &&
                              (networkInfo.value.effectiveType === '4g' || networkInfo.value.effectiveType === '3g');

        videoPlayers.value[key] = videojs(element, {
          autoplay: shouldAutoplay ? 'muted' : false, // 根據網絡狀況決定是否自動播放
          muted: true, // 預設靜音以符合自動播放政策
          loop: true, // 無限循環播放
          preload: networkInfo.value.saveData ? 'none' : 'metadata', // 節省流量模式下不預載
          fluid: true,
          controlBar: false, // 直接禁用控制欄
          playsinline: true, // 在移動設備上內聯播放
        }, () => {
          console.log(`Video player initialized for ${key}`);

          // 若使用者已互動過，立即解除新播放器靜音
          if (hasUserInteracted.value) {
            videoPlayers.value[key]?.muted(false);
          }

          // 確保影片能夠自動播放
          const player = videoPlayers.value[key];
          if (player) {
            // 設置循環播放
            player.loop(true);

            // 監聽播放器就緒事件
            player.ready(() => {
              console.log(`Player ${key} is ready`);
              // 延遲檢查可見性，確保DOM已完全渲染
              setTimeout(() => {
                checkVideoVisibility();
              }, 100);
            });

            // 監聽播放事件
            player.on('play', () => {
              console.log(`Video ${key} started playing`);
              // 暫停其他正在播放的影片（可選）
              // pauseOtherVideos(key);
            });

            // 監聽暫停事件
            player.on('pause', () => {
              console.log(`Video ${key} paused`);
            });

            // 監聽載入完成事件
            player.on('loadeddata', () => {
              console.log(`Video ${key} loaded data`);
            });

            // 監聽可以播放事件
            player.on('canplay', () => {
              console.log(`Video ${key} can play`);
              // 如果影片在可見區域，嘗試播放
              setTimeout(() => checkVideoVisibility(), 100);
            });

            // 監聽錯誤事件
            player.on('error', (error) => {
              console.error(`Video ${key} error:`, error);
              // 可以在這裡添加錯誤恢復邏輯
            });

            // 監聽音量變化（用戶互動後解除靜音）
            player.on('volumechange', () => {
              if (!player.muted() && !hasUserInteracted.value) {
                hasUserInteracted.value = true;
              }
            });
          }
        });
      }
    });
  });
}

// 暫停其他正在播放的影片（可選功能）
function pauseOtherVideos(excludeKey: string) {
  Object.entries(videoPlayers.value).forEach(([key, player]) => {
    if (key !== excludeKey && player && !player.isDisposed() && !player.paused()) {
      console.log(`Pausing other video: ${key}`);
      player.pause();
    }
  });
}

// 切換影片播放/暫停狀態
function toggleVideoPlayback(videoKey: string) {
  const player = videoPlayers.value[videoKey];
  if (!player || player.isDisposed()) {
    console.log(`Player ${videoKey} not found or disposed`);
    return;
  }

  handleFirstInteraction(); // 確保用戶已互動

  if (player.paused()) {
    console.log(`Manually playing video: ${videoKey}`);
    player.play()?.catch(error => {
      console.error(`Error manually playing video ${videoKey}:`, error);
    });
  } else {
    console.log(`Manually pausing video: ${videoKey}`);
    player.pause();
  }
}

// 檢查影片是否在視窗中間
function checkVideoVisibility() {
  const viewportHeight = window.innerHeight;
  const middleStart = viewportHeight * 0.25;
  const middleEnd = viewportHeight * 0.75;
  console.log(`Checking video visibility. Viewport Height: ${viewportHeight}, Middle Zone: ${middleStart}-${middleEnd}`);

  Object.entries(videoPlayers.value).forEach(([key, player]) => {
    if (player && !player.isDisposed() && player.el()) {
      const playerElement = player.el();
      const rect = playerElement.getBoundingClientRect();
      
      if (rect.width === 0 || rect.height === 0) {
        return; 
      }
      
      const elementTop = rect.top;
      const elementBottom = rect.bottom;
      const elementHeight = rect.height;
      const elementCenterY = elementTop + elementHeight / 2;

      const isInViewport = elementBottom > 0 && elementTop < viewportHeight;
      const isInMiddle = elementCenterY >= middleStart && elementCenterY <= middleEnd;
      
      console.log(
        `Video [${key}]: Top=${elementTop.toFixed(0)}, ` +
        `Bottom=${elementBottom.toFixed(0)}, ` +
        `Center=${elementCenterY.toFixed(0)}, ` +
        `inViewport=${isInViewport}, inMiddle=${isInMiddle}, ` +
        `Paused=${player.paused()}`
      );

      if (isInViewport && isInMiddle) {
        if (player.paused()) {
          console.log(`Video [${key}]: Attempting to play...`);
          // 確保影片已準備好播放
          if (player.readyState() >= 2) { // HAVE_CURRENT_DATA
            player.play()?.catch(error => {
              if (error.name !== 'NotAllowedError') {
                console.error(`Error playing video ${key}:`, error);
              } else {
                console.log(`Video ${key}: Autoplay blocked, user interaction required`);
              }
            });
          } else {
            // 如果影片還沒準備好，等待一下再嘗試
            console.log(`Video [${key}]: Not ready yet, waiting...`);
            setTimeout(() => {
              if (player.readyState() >= 2 && player.paused()) {
                player.play()?.catch(error => {
                  console.error(`Delayed play error for video ${key}:`, error);
                });
              }
            }, 500);
          }
        }
      } else {
        if (!player.paused()) {
          console.log(`Video [${key}]: Pausing...`);
          player.pause();
        }
      }
    }
  });
}

// 使用 throttle 包裹 checkVideoVisibility，每 200ms 最多執行一次
const throttledCheckVideoVisibility = throttle(() => {
    console.log('Throttled check executing...');
    checkVideoVisibility();
}, 200, {
  leading: false, 
  trailing: true, 
});

// 取得貼文
async function fetchPosts() {
  if (!lastId.value) posts.value = [];
  isLoading.value = true;
  try {
    const params: Record<string, any> = { limit: limit.value };
    if (lastId.value) params.lastId = lastId.value;
    const { data } = await axios.get('/posts', { params });
    const { posts: fetchedPosts, nextLastId } = data;
    const currentUserId = localStorage.getItem('userId');
    const mapped = fetchedPosts.map((p: any) => ({
      ...p,
      likes: p.likes.length,
      isLiked: currentUserId ? p.likes.includes(currentUserId) : false,
      expanded: false,
      showComments: false,
      showMenu: false,
      newComment: '',
      comments: p.comments.map((c: any) => ({
        ...c,
        likes: c.likes?.length || 0,
        isLiked: currentUserId ? (c.likes || []).includes(currentUserId) : false,
        showMenu: false,
      })),
    }));
    if (!lastId.value) {
      posts.value = mapped;
    } else {
      posts.value.push(...mapped);
    }
    lastId.value = nextLastId;
    initializeVideoPlayers();
  } catch (e: any) {
    console.error(e);
    apiError.value = `無法取得貼文：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  } finally {
    isLoading.value = false;
  }
}

// 滾動加載和可見性檢查
function handleScroll() {
  console.log('Scroll event detected!');
  const bottomReached = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100;
  if (bottomReached && !isLoading.value && lastId.value) {
    fetchPosts();
  }
  throttledCheckVideoVisibility();
}

// 監聽 posts 變化，重新初始化播放器
watch(posts, () => {
  initializeVideoPlayers();
}, { deep: true });

onMounted(async () => {
  // 檢測網絡狀況
  detectNetworkConditions();

  fetchCurrentUser();
  fetchPosts().then(() => {
    setTimeout(() => {
        console.log('Running delayed initial visibility check...');
        checkVideoVisibility();
    }, 100);
  });

  // 獲取上傳限制
  try {
    uploadLimits.value = await getUploadLimits();
  } catch (error) {
    console.error('獲取上傳限制失敗:', error);
  }

  window.addEventListener('scroll', handleScroll);
  document.addEventListener('click', closeAllMenus);

  // 首次使用者互動後解除影片靜音
  document.addEventListener('touchstart', handleFirstInteraction, { once: true, passive: true });
  document.addEventListener('click', handleFirstInteraction, { once: true });

  // Pull-to-refresh 事件
  document.addEventListener('touchstart', onGlobalTouchStart, { passive: true });
  document.addEventListener('touchmove', onGlobalTouchMove, { passive: true });
  document.addEventListener('touchend', onGlobalTouchEnd);
});

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll);
  throttledCheckVideoVisibility.cancel?.();
  Object.values(videoPlayers.value).forEach(player => {
    if (player && !player.isDisposed()) {
      player.dispose();
    }
  });
  videoPlayers.value = {};
  document.removeEventListener('click', closeAllMenus);
  document.removeEventListener('touchstart', handleFirstInteraction);
  document.removeEventListener('click', handleFirstInteraction);
  document.removeEventListener('touchstart', onGlobalTouchStart);
  document.removeEventListener('touchmove', onGlobalTouchMove);
  document.removeEventListener('touchend', onGlobalTouchEnd);
});

// 格式化時間
function formatTime(dateStr: string) {
  const date = new Date(dateStr);
  const now = new Date();
  const diff = Math.floor((now.getTime() - date.getTime()) / 1000);
  if (diff < 60) return '剛剛';
  if (diff < 3600) return `${Math.floor(diff / 60)} 分鐘前`;
  if (diff < 86400) return `${Math.floor(diff / 3600)} 小時前`;
  if (diff < 604800) return `${Math.floor(diff / 86400)} 天前`;
  return `${date.getFullYear()}/${(date.getMonth()+1).toString().padStart(2,'0')}/${date.getDate().toString().padStart(2,'0')}`;
}

// 開啟 / 關閉
function openPostCreation() {
  isPostCreationOpen.value = true;
  document.body.style.overflow = 'hidden';
}
function closePostCreation() {
  isPostCreationOpen.value = false;
  document.body.style.overflow = '';
  newPostContent.value = '';
  selectedMedia.value = [];
  // 清理壓縮相關狀態
  isCompressing.value = false;
  compressionProgress.value = null;
  compressionResults.value = [];
  // 清理上傳相關狀態
  isUploading.value = false;
  uploadProgress.value = { overall: 0, current: null };
  uploadResults.value = [];
}

// 發布貼文
const newPostContent = ref('');
const selectedMedia = ref<Media[]>([]);

// 檔案壓縮相關
const isCompressing = ref(false);
const compressionProgress = ref<CompressionProgress | null>(null);
const compressionResults = ref<FileCompressionResult[]>([]);

// 直接上傳相關
const isUploading = ref(false);
const uploadProgress = ref<{ overall: number; current: UploadProgress | null }>({ overall: 0, current: null });
const uploadResults = ref<UploadResult[]>([]);
const uploadLimits = ref<any>(null);

// 計算發布按鈕是否可用
const canPublish = computed(() => {
  // 基本條件：必須有內容或媒體
  const hasContent = newPostContent.value.trim().length > 0 || selectedMedia.value.length > 0;

  // 如果沒有內容，不能發布
  if (!hasContent) return false;

  // 如果正在壓縮檔案，不能發布
  if (isCompressing.value) return false;

  // 如果正在上傳檔案，不能發布
  if (isUploading.value) return false;

  // 如果正在發布，不能重複發布
  if (isPublishing.value) return false;

  // 如果有選擇的媒體檔案，檢查是否都已壓縮完成
  if (selectedMedia.value.length > 0) {
    // 檢查是否所有檔案都已經壓縮完成
    const allFilesProcessed = selectedMedia.value.every(media => media.file !== undefined);
    if (!allFilesProcessed) return false;
  }

  return true;
});

async function createPost() {
  if (!newPostContent.value.trim() && selectedMedia.value.length === 0) return;
  isPublishing.value = true;

  try {
    let mediaUrls: any[] = [];

    // 如果有媒體檔案，先上傳到 GCS
    if (selectedMedia.value.length > 0) {
      isUploading.value = true;
      uploadProgress.value = { overall: 0, current: null };

      try {
        // 使用壓縮後的檔案進行上傳
        const filesToUpload = selectedMedia.value
          .map(media => media.file)
          .filter(file => file !== undefined) as File[];

        // 判斷檔案類型（影片或圖片）
        const hasVideo = filesToUpload.some(file => file.type.startsWith('video/'));
        const fileType = hasVideo ? 'video' : 'image';

        try {
          // 嘗試直接上傳到 GCS
          const uploadResults = await directUploadFiles(
            filesToUpload,
            fileType,
            (overall, current) => {
              uploadProgress.value = { overall, current };
            }
          );

          // 轉換為貼文需要的格式
          mediaUrls = uploadResults.map(result => ({
            type: result.mimeType.startsWith('video/') ? 'video' : 'image',
            url: result.publicUrl,
            mimeType: result.mimeType
          }));

        } catch (directUploadError) {
          console.warn('直接上傳失敗，回退到傳統上傳:', directUploadError);

          // 回退到傳統上傳方式
          isUploading.value = false;
          const formData = new FormData();
          formData.append('content', newPostContent.value);

          selectedMedia.value.forEach((media) => {
            if (media.file) {
              formData.append('media', media.file);
            }
          });

          await axios.post('/posts', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });

          // 成功後直接返回，跳過後續的直接上傳邏輯
          lastId.value = null;
          await fetchPosts();

          // 確保新影片能夠正確初始化和播放
          setTimeout(() => {
            console.log('Fallback upload completed, checking video visibility...');
            checkVideoVisibility();
          }, 500);

          closePostCreation();
          showSuccessToast.value = true;
          setTimeout(() => (showSuccessToast.value = false), 3000);
          return;
        }

      } catch (uploadError) {
        console.error('檔案上傳失敗:', uploadError);
        apiError.value = `檔案上傳失敗：${uploadError instanceof Error ? uploadError.message : '未知錯誤'}`;
        setTimeout(() => (apiError.value = null), 5000);
        return;
      } finally {
        isUploading.value = false;
      }
    }

    // 創建貼文（只傳送內容和媒體 URL）
    const postData = {
      content: newPostContent.value,
      media: mediaUrls
    };

    await axios.post('/posts', postData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 重設分頁游標以重新拉取最新貼文（包含剛剛發布的）
    lastId.value = null;
    await fetchPosts();

    // 確保新影片能夠正確初始化和播放
    setTimeout(() => {
      console.log('Post creation completed, checking video visibility...');
      checkVideoVisibility();
    }, 500);

    closePostCreation();
    showSuccessToast.value = true;
    setTimeout(() => (showSuccessToast.value = false), 3000);

  } catch (e: any) {
    console.error(e);
    apiError.value = `發布貼文失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  } finally {
    isPublishing.value = false;
  }
}

// 媒體處理
async function handleMediaSelect(e: Event) {
  const input = e.target as HTMLInputElement;
  if (!input.files) return;
  const files = Array.from(input.files);
  const existingTypes = selectedMedia.value.map(m => m.type);
  const newTypes = files.map(file => file.type.startsWith('video/') ? 'video' : 'image');
  if (existingTypes.includes('video')) {
    alert('影片貼文只能包含單一影片'); input.value = ''; return;
  }
  if (newTypes.includes('video') && newTypes.includes('image')) {
    alert('不能同時上傳圖片與影片'); input.value = ''; return;
  }
  if (newTypes.filter(t => t === 'video').length > 1) {
    alert('只能選擇一段影片'); input.value = ''; return;
  }
  if (selectedMedia.value.length + files.length > 9) {
    alert('最多 9 個檔案'); input.value = ''; return;
  }

  // 檔案驗證
  if (uploadLimits.value) {
    for (const file of files) {
      const fileType = file.type.startsWith('video/') ? 'video' : 'image';
      const validation = validateFile(file, fileType, uploadLimits.value);
      if (!validation.valid) {
        alert(`檔案 ${file.name} 驗證失敗: ${validation.error}`);
        input.value = '';
        return;
      }
    }
  }

  // 開始壓縮處理
  isCompressing.value = true;
  compressionProgress.value = null;

  try {
    // 壓縮檔案
    const results = await compressFiles(files, {
      enabled: true,
      image: {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8,
        format: 'jpeg',
        maxSizeKB: 1024
      },
      video: {
        maxWidth: 1280,
        maxHeight: 720,
        quality: 0.7,
        maxSizeKB: 10240,
        maxDuration: 300
      }
    }, (progress) => {
      compressionProgress.value = progress;
    });

    compressionResults.value = results;

    // 為每個壓縮後的檔案創建預覽
    for (const result of results) {
      const reader = new FileReader();
      reader.onload = ev => {
        if (ev.target && ev.target.result) {
          selectedMedia.value.push({
            type: result.file.type.startsWith('video/') ? 'video' : 'image',
            url: '',
            preview: ev.target.result as string,
            file: result.file,
            mimeType: result.file.type,
            originalSize: result.originalSize,
            compressedSize: result.compressedSize,
            compressionRatio: result.compressionRatio
          });
        }
      };
      reader.readAsDataURL(result.file);
    }
  } catch (error) {
    console.error('檔案壓縮失敗:', error);
    // 如果壓縮失敗，使用原始檔案
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = ev => {
        if (ev.target && ev.target.result) {
          selectedMedia.value.push({
            type: file.type.startsWith('video/') ? 'video' : 'image',
            url: '', preview: ev.target.result as string,
            file, mimeType: file.type,
          });
        }
      };
      reader.readAsDataURL(file);
    });
  } finally {
    isCompressing.value = false;
    compressionProgress.value = null;
  }

  input.value = '';
}
function removeMedia(idx: number) {
  selectedMedia.value.splice(idx, 1);
}

// 互動
function toggleExpand(post: Post) {
  post.expanded = !post.expanded;
}
async function toggleLike(post: Post) {
  try {
    const { data } = await axios.post(`/posts/${post._id}/like`);
    post.likes = data.likes;
    post.isLiked = data.isLiked;
  } catch (e: any) {
    console.error(e);
    apiError.value = `按讚失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}
function toggleComments(post: Post) {
  post.showComments = !post.showComments;
}
async function addComment(post: Post) {
  if (!post.newComment.trim()) return;
  try {
    const { data } = await axios.post(`/posts/${post._id}/comments`, { content: post.newComment });
    post.comments.push(data);
    post.newComment = '';
  } catch (e: any) {
    console.error(e);
    apiError.value = `新增評論失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}

// 新增：處理評論按讚
async function toggleCommentLike(post: Post, comment: Comment) {
  try {
    const { data } = await axios.post(`/posts/${post._id}/comments/${comment._id}/like`);
    comment.likes = data.likes;
    comment.isLiked = data.isLiked;
  } catch (e: any) {
    console.error(e);
    apiError.value = `評論按讚失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}

// 處理頭像加載錯誤
function handleAvatarError(event: Event) {
  const imgElement = event.target as HTMLImageElement;
  if (imgElement) {
    imgElement.src = defaultAvatar.value;
  }
}

// 檢查是否為貼文擁有者
function isPostOwner(post: Post): boolean {
  const currentUserId = localStorage.getItem('userId');
  return post.author._id === currentUserId;
}

// 檢查是否為評論擁有者
function isCommentOwner(comment: Comment): boolean {
  const currentUserId = localStorage.getItem('userId');
  return comment.author._id === currentUserId;
}

// 貼文選單
function togglePostMenu(post: Post) {
  posts.value.forEach(p => {
    if (p._id !== post._id) p.showMenu = false;
  });
  post.showMenu = !post.showMenu;
}

// 評論選單
function toggleCommentMenu(post: Post, comment: Comment) {
  post.comments.forEach(c => {
    if (c._id !== comment._id) c.showMenu = false;
  });
  comment.showMenu = !comment.showMenu;
}

// 編輯貼文
function openPostEdit(post: Post) {
  editPostDialog.value = {
    show: true,
    postId: post._id,
    content: post.content
  };
  editMedia.value = post.media.map(m => ({ ...m }));
  editNewMedia.value = [];
  mediaToRemove.value = [];
  post.showMenu = false;
}

async function updatePost() {
  try {
    const formData = new FormData();
    formData.append('content', editPostDialog.value.content);
    formData.append('removeMedia', JSON.stringify(mediaToRemove.value));
    editNewMedia.value.forEach(m => m.file && formData.append('media', m.file));
    const { data } = await axios.put(
      `/posts/${editPostDialog.value.postId}`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    const postIndex = posts.value.findIndex(p => p._id === editPostDialog.value.postId);
    if (postIndex > -1) {
      const post = posts.value[postIndex];
      post.content = data.content;
      post.isEdited = data.isEdited;
      post.media = data.media;
      post.likes = Array.isArray(data.likes) ? data.likes.length : data.likes;
    }
    closeEditPostDialog();
    initializeVideoPlayers();
  } catch (e: any) {
    console.error(e);
    apiError.value = `編輯貼文失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}

function closeEditPostDialog() {
  editPostDialog.value = { show: false, postId: '', content: '' };
}

// 編輯評論
function openCommentEdit(post: Post, comment: Comment) {
  editCommentDialog.value = {
    show: true,
    postId: post._id,
    commentId: comment._id,
    content: comment.content
  };
  comment.showMenu = false;
}

async function updateComment() {
  try {
    const { data } = await axios.put(
      `/posts/${editCommentDialog.value.postId}/comments/${editCommentDialog.value.commentId}`,
      { content: editCommentDialog.value.content }
    );
    
    const postIndex = posts.value.findIndex(p => p._id === editCommentDialog.value.postId);
    if (postIndex > -1) {
      const commentIndex = posts.value[postIndex].comments.findIndex(
        c => c._id === editCommentDialog.value.commentId
      );
      if (commentIndex > -1) {
        posts.value[postIndex].comments[commentIndex].content = data.content;
        posts.value[postIndex].comments[commentIndex].isEdited = data.isEdited;
      }
    }
    
    closeEditCommentDialog();
  } catch (e: any) {
    console.error(e);
    apiError.value = `編輯評論失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}

function closeEditCommentDialog() {
  editCommentDialog.value = { show: false, postId: '', commentId: '', content: '' };
}

// 刪除貼文
function confirmDeletePost(post: Post) {
  deleteDialog.value = {
    show: true,
    type: 'post',
    postId: post._id,
    commentId: '',
    message: '確定要刪除這則貼文嗎？此操作無法復原。'
  };
  post.showMenu = false;
}

// 刪除評論
function confirmDeleteComment(post: Post, comment: Comment) {
  deleteDialog.value = {
    show: true,
    type: 'comment',
    postId: post._id,
    commentId: comment._id,
    message: '確定要刪除這則評論嗎？此操作無法復原。'
  };
  comment.showMenu = false;
}

async function confirmDelete() {
  try {
    if (deleteDialog.value.type === 'post') {
      await axios.delete(`/posts/${deleteDialog.value.postId}`);
      posts.value = posts.value.filter(p => p._id !== deleteDialog.value.postId);
    } else if (deleteDialog.value.type === 'comment') {
      await axios.delete(
        `/posts/${deleteDialog.value.postId}/comments/${deleteDialog.value.commentId}`
      );
      
      const postIndex = posts.value.findIndex(p => p._id === deleteDialog.value.postId);
      if (postIndex > -1) {
        posts.value[postIndex].comments = posts.value[postIndex].comments.filter(
          c => c._id !== deleteDialog.value.commentId
        );
      }
    }
    closeDeleteDialog();
  } catch (e: any) {
    console.error(e);
    apiError.value = `刪除失敗：${e.response?.data?.message || e.message || '未知錯誤'}`;
    setTimeout(() => (apiError.value = null), 5000);
  }
}

function closeDeleteDialog() {
  deleteDialog.value = { show: false, type: '', postId: '', commentId: '', message: '' };
}

// 媒體檢視器
function openMediaViewer(media: Media[], startIndex: number) {
  mediaViewer.value = {
    show: true,
    media: media,
    currentIndex: startIndex >= media.length ? 0 : startIndex
  };
  document.body.style.overflow = 'hidden';
}

function closeMediaViewer() {
  mediaViewer.value.show = false;
  document.body.style.overflow = '';
}

function navigateMedia(direction: number) {
  const newIndex = mediaViewer.value.currentIndex + direction;
  if (newIndex >= 0 && newIndex < mediaViewer.value.media.length) {
    mediaViewer.value.currentIndex = newIndex;
  }
}

// 手勢處理函數
function onTouchStart(e: TouchEvent) {
  if (e.touches && e.touches.length) {
    touchStartX.value = e.touches[0].clientX;
    touchStartY.value = e.touches[0].clientY;
    touchDirectionSet.value = false;
  }
}
function onTouchMove(e: TouchEvent) {
  if (touchDirectionSet.value || !e.touches || e.touches.length === 0) return;
  const deltaX = e.touches[0].clientX - touchStartX.value;
  const deltaY = e.touches[0].clientY - touchStartY.value;
  if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
    touchDirectionSet.value = true;
    navigateMedia(deltaX > 0 ? -1 : 1);
  } else if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 50) {
    touchDirectionSet.value = true;
    closeMediaViewer();
  }
}
function onTouchEnd() {
  touchDirectionSet.value = false;
}

// 新增：編輯貼文媒體選擇處理
function handleEditMediaSelect(e: Event) {
  const input = e.target as HTMLInputElement;
  if (!input.files) return;
  const files = Array.from(input.files);
  const existingTypes = editMedia.value.map(m => m.type).concat(editNewMedia.value.map(m => m.type));
  const newTypes = files.map(file => file.type.startsWith('video/') ? 'video' : 'image');
  if (existingTypes.includes('video')) {
    alert('影片貼文只能包含單一影片'); input.value = ''; return;
  }
  if (newTypes.includes('video') && newTypes.includes('image')) {
    alert('不能同時上傳圖片與影片'); input.value = ''; return;
  }
  if (newTypes.filter(t => t === 'video').length > 1) {
    alert('只能選擇一段影片'); input.value = ''; return;
  }
  if (selectedMedia.value.length + files.length > 9) {
    alert('最多 9 個檔案'); input.value = ''; return;
  }
  files.forEach(file => {
    const reader = new FileReader();
    reader.onload = ev => {
      if (ev.target && ev.target.result) {
        editNewMedia.value.push({
          type: file.type.startsWith('video/') ? 'video' : 'image',
          preview: ev.target.result as string, file,
          mimeType: file.type, url: ''
        });
      }
    };
    reader.readAsDataURL(file);
  });
  input.value = '';
}

// 新增：移除舊媒體
function removeEditMedia(media: Media, idx: number) {
  mediaToRemove.value.push(media._id!);
  editMedia.value.splice(idx, 1);
}

// 新增：移除新上傳媒體
function removeNewEditMedia(idx: number) {
  editNewMedia.value.splice(idx, 1);
}

// 開啟按讚名單對話框
async function openLikesDialog(post: Post) {
  if (post.likes === 0) return; // 0 人喜歡不彈窗
  try {
    const { data } = await axios.get(`/posts/${post._id}/likes`);
    likesDialog.value.users = data;
    likesDialog.value.show = true;
  } catch (e: any) {
    console.error(e);
    apiError.value = '無法取得按讚名單';
    setTimeout(() => (apiError.value = null), 3000);
  }
}

// 關閉按讚名單對話框
function closeLikesDialog() {
  likesDialog.value.show = false;
  likesDialog.value.users = [];
}

// ---------- 全局點擊關閉貼文/評論選單 ----------
function closeAllMenus() {
  posts.value.forEach(p => {
    p.showMenu = false;
    p.comments.forEach(c => {
      c.showMenu = false;
    });
  });
}

/* --------------------------- Pull-to-Refresh --------------------------- */
const isPulling = ref(false);
const pullStartY = ref(0);
const pullThreshold = 100; // px

function onGlobalTouchStart(e: TouchEvent) {
  if (window.scrollY === 0 && e.touches && e.touches.length) {
    pullStartY.value = e.touches[0].clientY;
    isPulling.value = true;
  }
}

function onGlobalTouchMove(e: TouchEvent) {
  if (!isPulling.value) return;
  const dist = e.touches[0].clientY - pullStartY.value;
  if (dist > pullThreshold) {
    isPulling.value = false;
    triggerFeedRefresh();
  }
}

function onGlobalTouchEnd() {
  isPulling.value = false;
}

async function triggerFeedRefresh() {
  if (isLoading.value) return;
  console.log('Pull-to-refresh triggered');
  lastId.value = null;
  await fetchPosts();
}
</script>

<style scoped>
.app-container {
  font-family: 'Noto Sans TC', sans-serif;
}
.post-card { transition: all 0.3s ease; }
.post-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.05); }
.line-clamp-3 { display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden; }
textarea { min-height: 100px; }
/* 確保圖片在網格佈局中不會超出圓角容器 */
.grid img, .grid video {
  display: block;
  width: 100%;
  height: 100%;
}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin:0; }
input[type=number] { -moz-appearance: textfield; }

/* 新增: Swiper 容器的基本樣式 */
.swiper-container {
  position: relative; /* 讓絕對定位的按鈕相對此容器 */
}
.swiper-wrapper {
  /* display: flex; 由 Tailwind class 提供 */
  /* transition: transform 0.3s ease; 由 Tailwind class 提供 */
}
.swiper-slide {
  /* width: 100%; 由 Tailwind class 提供 */
  /* flex-shrink: 0; 由 Tailwind class 提供 */
  /* display: flex; align-items: center; justify-content: center; 由 Tailwind class 提供 */
}

/* Video.js 自訂樣式 (可選) */
.video-js {
  /* 確保播放按鈕在中間 */
  display: block; 
  /* 新增：強制容器寬度 100%，覆蓋 video.js 內聯設定 */
  width: 100% !important;
  /* 新增：若需要可保持高度自動 */
  height: auto !important;
  /* 新增：確保 video 元素本身也是塊級或行內塊，以便尺寸正確計算 */
  /* width: 100%;  由 Tailwind class 提供 */
  /* height: 100%; 由 Tailwind class 提供 (用於網格項/aspect-video) */
}
/* 如果只想顯示大的播放按鈕，隱藏控制條 */
/* .video-js .vjs-control-bar {
  display: none !important;
} */

/* 確保大播放按鈕垂直居中 (如果 vjs-big-play-centered 不夠) */
.video-js .vjs-big-play-button {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0; /* 重置可能存在的 margin */
}

/* 可以調整控制條只顯示播放按鈕 */
.video-js .vjs-control-bar > *:not(.vjs-play-control):not(.vjs-volume-panel) { /* 如果加了 volumePanel, 這裡也要排除 */
 /* display: none;  /* 如果只想留播放鈕，取消此註解 */
}
.video-js .vjs-play-control {
  /* 可以調整播放按鈕樣式 */
}

/* 新增: 強制內部 video 元素寬度 100%、高度自動 */
.video-js .vjs-tech {
  width: 100% !important; /* 覆蓋 video.js 可能的內聯樣式 */
  height: auto !important; /* 允許瀏覽器根據寬度和比例計算高度 */
}


:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #F3F4F6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: block;
  min-width: 320px;
  min-height: 100vh;
  overscroll-behavior-y: none;
  -ms-scroll-chaining: none;
  overscroll-behavior: none;
  position: relative;
  overflow-y: auto;
  touch-action: pan-x pan-y;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: border-color 0.25s;
  -webkit-tap-highlight-color: transparent;
}
button:hover,
button:active {
  border-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}
button:focus,
button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

</style> 