const dotenv = require('dotenv');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const faker = require('@faker-js/faker').faker;
const bcrypt = require('bcryptjs');

dotenv.config();

const connectDB = require('../config/db');
const User = require('../models/User');

const jwtSecret = process.env.JWT_SECRET || 'mysecret';

(async function () {
  try {
    await connectDB();

    const usersData = [];

    for (let i = 0; i < 10; i++) {
      const name = faker.person.fullName();
      const email = faker.internet.email({ allowSpecialCharacters: false }).toLowerCase();
      const username = faker.internet.userName({ firstName: name.split(' ')[0], lastName: name.split(' ')[1] || '' }).toLowerCase() + faker.number.int({ min: 100, max: 999 });
      const passwordPlain = faker.internet.password({ length: 10, memorable: true });
      const avatar = faker.image.avatar();

      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash(passwordPlain, salt);

      const user = await User.create({ name, username, email, password: passwordHash, avatar });
      const token = jwt.sign({ id: user._id }, jwtSecret, { expiresIn: '30d' });

      usersData.push({
        id: user._id.toString(),
        name,
        username,
        email,
        password: passwordPlain,
        token,
        loginUrl: `/api/auth/login (POST) { email: '${email}', password: '${passwordPlain}' }`,
      });
    }

    console.log('==== 10 個使用者已建立 ====');
    usersData.forEach((u, idx) => {
      console.log(`\n#${idx + 1}`);
      console.log(`ID       : ${u.id}`);
      console.log(`Name     : ${u.name}`);
      console.log(`Username : ${u.username}`);
      console.log(`Email    : ${u.email}`);
      console.log(`Password : ${u.password}`);
      console.log(`JWT      : ${u.token}`);
      console.log(`Login    : ${u.loginUrl}`);
    });

    process.exit(0);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
})(); 