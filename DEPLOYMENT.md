# SoTime App Docker 部署指南

## 🚀 快速開始

### 前置需求
- Docker 20.10+
- Docker Compose 2.0+
- Git
- 至少 2GB RAM 和 10GB 磁盤空間

### 一鍵部署
```bash
# 克隆項目
git clone <your-repo-url>
cd WallServer

# 設置執行權限
chmod +x deploy.sh

# 啟動服務
./deploy.sh prod start
```

訪問 http://localhost:6666 即可使用應用！

## 📋 詳細部署步驟

### 1. 環境準備

#### 安裝 Docker (Ubuntu/Debian)
```bash
# 更新包索引
sudo apt update

# 安裝必要的包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密鑰
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 設置穩定版倉庫
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安裝 Docker Engine
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 啟動 Docker 服務
sudo systemctl start docker
sudo systemctl enable docker

# 將用戶添加到 docker 組
sudo usermod -aG docker $USER
```

#### 安裝 Docker Compose
```bash
# 下載 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 設置執行權限
sudo chmod +x /usr/local/bin/docker-compose

# 驗證安裝
docker-compose --version
```

### 2. 項目配置

#### 環境變量配置
```bash
# 複製生產環境配置
cp .env.production .env

# 編輯配置文件
nano .env
```

重要配置項：
- `JWT_SECRET`: 更改為強密碼
- `MONGO_ROOT_PASSWORD`: MongoDB 根密碼
- `MONGO_PASSWORD`: 應用數據庫密碼

#### 防火牆配置
```bash
# 開放必要端口
sudo ufw allow 6666/tcp  # 應用端口
sudo ufw allow 22/tcp    # SSH 端口
sudo ufw enable
```

### 3. 部署操作

#### 使用部署腳本
```bash
# 構建應用
./deploy.sh prod build

# 啟動服務
./deploy.sh prod start

# 查看狀態
./deploy.sh prod status

# 查看日誌
./deploy.sh prod logs

# 重啟服務
./deploy.sh prod restart

# 停止服務
./deploy.sh prod stop

# 清理資源
./deploy.sh prod clean
```

#### 手動部署
```bash
# 構建 Docker 映像
docker build -t sotime-app:latest .

# 啟動服務
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f
```

## 🔧 CI/CD 自動部署

### GitHub Actions 設置

#### 1. 服務器準備
```bash
# 在服務器上創建部署目錄
sudo mkdir -p /opt/sotime
sudo chown $USER:$USER /opt/sotime

# 生成 SSH 密鑰對（如果沒有）
ssh-keygen -t rsa -b 4096 -C "deploy@sotime"

# 將公鑰添加到 authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
```

#### 2. GitHub Secrets 配置
在 GitHub 倉庫設置中添加以下 Secrets：

| Secret 名稱 | 描述 | 示例值 |
|------------|------|--------|
| `SERVER_HOST` | 服務器 IP 地址 | `*************` |
| `SERVER_USER` | SSH 用戶名 | `ubuntu` |
| `SERVER_SSH_KEY` | SSH 私鑰 | `-----BEGIN RSA PRIVATE KEY-----...` |
| `SERVER_PORT` | SSH 端口 | `22` |
| `DOCKER_USERNAME` | Docker Hub 用戶名 | `your_username` |
| `DOCKER_PASSWORD` | Docker Hub 密碼 | `your_password` |
| `WEBHOOK_URL` | 通知 Webhook URL | `https://hooks.slack.com/...` |

#### 3. 自動部署流程
推送到 `main` 分支時會自動觸發部署：

1. **測試階段**: 運行測試和構建檢查
2. **構建階段**: 構建 Docker 映像
3. **部署階段**: 部署到服務器
4. **通知階段**: 發送部署結果通知

## 🔍 監控和維護

### 健康檢查
```bash
# 檢查應用健康狀態
curl http://localhost:6666/health

# 檢查服務狀態
docker-compose ps

# 查看資源使用情況
docker stats
```

### 日誌管理
```bash
# 查看應用日誌
docker-compose logs app

# 查看 MongoDB 日誌
docker-compose logs mongodb

# 查看 Nginx 日誌
docker-compose logs app | grep nginx
```

### 數據備份
```bash
# 使用部署腳本備份
./deploy.sh prod backup

# 手動備份 MongoDB
docker-compose exec mongodb mongodump --out /tmp/backup
docker cp $(docker-compose ps -q mongodb):/tmp/backup ./backup-$(date +%Y%m%d)

# 備份上傳文件
tar -czf uploads-backup-$(date +%Y%m%d).tar.gz uploads/
```

### 更新應用
```bash
# 拉取最新代碼
git pull origin main

# 重新構建和部署
./deploy.sh prod build
./deploy.sh prod restart
```

## 🛠️ 故障排除

### 常見問題

#### 1. 端口被占用
```bash
# 查看端口使用情況
sudo netstat -tlnp | grep 6666

# 停止占用端口的進程
sudo kill -9 <PID>
```

#### 2. Docker 映像構建失敗
```bash
# 清理 Docker 緩存
docker system prune -a

# 重新構建
docker build --no-cache -t sotime-app:latest .
```

#### 3. 數據庫連接失敗
```bash
# 檢查 MongoDB 容器狀態
docker-compose logs mongodb

# 重啟 MongoDB
docker-compose restart mongodb
```

#### 4. 前端資源加載失敗
```bash
# 檢查 Nginx 配置
docker-compose exec app nginx -t

# 重新加載 Nginx 配置
docker-compose exec app nginx -s reload
```

### 性能優化

#### 1. 資源限制
在 `docker-compose.yml` 中添加資源限制：
```yaml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

#### 2. 日誌輪轉
```bash
# 配置 Docker 日誌輪轉
echo '{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}' | sudo tee /etc/docker/daemon.json

sudo systemctl restart docker
```

## 📞 支持

如遇到問題，請：
1. 查看日誌: `./deploy.sh prod logs`
2. 檢查健康狀態: `curl http://localhost:6666/health`
3. 提交 Issue 到 GitHub 倉庫

---

**部署成功後，您的 SoTime 應用將在 http://localhost:6666 運行！** 🎉
