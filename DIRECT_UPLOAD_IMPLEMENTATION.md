# 直接上傳到 GCS 實現說明

## 🚀 功能概述

已成功實現前端直接上傳檔案到 Google Cloud Storage 的方案，大幅提升上傳效能，特別是對於大檔案。

## 📊 效能提升

### 之前的流程：
```
前端 → Server → GCP Storage
- 檔案傳輸兩次
- Server 處理負擔重
- 延遲較高
```

### 現在的流程：
```
前端 → GCP Storage (直接)
- 檔案只傳輸一次
- Server 負擔輕
- 延遲更低
- 支援並行上傳
```

## 🔧 技術實現

### 1. 後端 API 端點

#### 新增路由：`/api/upload`

<augment_code_snippet path="routes/upload.js" mode="EXCERPT">
```javascript
// 生成上傳憑證
router.post('/signed-url', protect, async (req, res) => {
  // 驗證檔案參數
  // 生成 Signed URL
  // 返回上傳憑證
});

// 驗證上傳完成
router.post('/verify', protect, async (req, res) => {
  // 驗證檔案是否存在於 GCS
  // 返回驗證結果
});
```
</augment_code_snippet>

#### 更新的 GCS 工具：

<augment_code_snippet path="config/storage.js" mode="EXCERPT">
```javascript
// 生成 Signed URL 用於直接上傳
async function generateSignedUploadUrl(fileName, mimeType, expiresInMinutes = 15) {
  const options = {
    version: 'v4',
    action: 'write',
    expires: Date.now() + expiresInMinutes * 60 * 1000,
    contentType: mimeType,
  };
  const [signedUrl] = await file.getSignedUrl(options);
  return { signedUrl, publicUrl, fileName };
}
```
</augment_code_snippet>

### 2. 前端直接上傳工具

#### 新增工具：`client/src/utils/directUpload.ts`

<augment_code_snippet path="client/src/utils/directUpload.ts" mode="EXCERPT">
```typescript
// 完整的直接上傳流程
export async function directUploadFiles(
  files: File[],
  fileType: 'image' | 'video' | 'avatar' | 'cover' | 'banner' = 'image',
  onProgress?: (overall: number, current: UploadProgress | null) => void
): Promise<UploadResult[]> {
  // 1. 獲取上傳憑證
  // 2. 直接上傳到 GCS
  // 3. 驗證上傳完成
}
```
</augment_code_snippet>

### 3. 前端組件更新

#### 修改的發文流程：

<augment_code_snippet path="client/src/components/Wall.vue" mode="EXCERPT">
```typescript
async function createPost() {
  // 1. 壓縮檔案（如果需要）
  // 2. 直接上傳到 GCS
  const uploadResults = await directUploadFiles(filesToUpload, fileType);
  
  // 3. 只傳送媒體 URL 到後端
  const postData = {
    content: newPostContent.value,
    media: uploadResults.map(result => ({
      type: result.mimeType.startsWith('video/') ? 'video' : 'image',
      url: result.publicUrl,
      mimeType: result.mimeType
    }))
  };
  
  await axios.post('/posts', postData);
}
```
</augment_code_snippet>

## 🔒 安全性保障

### 1. 檔案驗證
- **前端驗證**：檔案類型、大小、數量限制
- **後端驗證**：生成 Signed URL 時再次驗證
- **GCS 驗證**：上傳完成後驗證檔案存在

### 2. 權限控制
- **認證要求**：必須登入才能獲取上傳憑證
- **時效限制**：Signed URL 15分鐘後過期
- **檔案隔離**：按用戶 ID 和時間戳生成唯一檔名

### 3. 檔案限制配置

<augment_code_snippet path="routes/upload.js" mode="EXCERPT">
```javascript
const FILE_LIMITS = {
  image: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    folder: 'images'
  },
  video: {
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['video/mp4', 'video/webm', 'video/quicktime'],
    folder: 'videos'
  }
};
```
</augment_code_snippet>

## 📱 用戶體驗改進

### 1. 雙重進度顯示
- **壓縮進度**：藍色進度條，顯示檔案壓縮狀態
- **上傳進度**：綠色進度條，顯示上傳到 GCS 的進度

### 2. 詳細進度資訊
- 總體進度百分比
- 當前處理檔案名稱
- 檔案傳輸速度和大小
- 剩餘時間估算

### 3. 錯誤處理
- 網路錯誤自動重試
- 檔案驗證失敗提示
- 上傳失敗回退機制

## 🔄 向後相容性

### 支援兩種上傳方式：

1. **直接上傳**（新方式）：
   - 前端壓縮 → 直接上傳到 GCS → 傳送 URL 到後端

2. **傳統上傳**（舊方式）：
   - 前端 → 後端 → GCS（保持現有 API 相容）

<augment_code_snippet path="routes/posts.js" mode="EXCERPT">
```javascript
// 支援兩種上傳方式
if (directMedia && Array.isArray(directMedia)) {
  // 直接上傳方式：使用前端提供的 URL
  media = directMedia.map(item => ({ ... }));
} else if (req.files && req.files.length > 0) {
  // 傳統上傳方式：處理 multer 檔案
  for (const file of req.files) { ... }
}
```
</augment_code_snippet>

## 📈 效能指標

### 預期改善：
- **上傳速度**：提升 50-80%（避免二次傳輸）
- **Server 負載**：減少 70%（不處理檔案內容）
- **並行處理**：支援多檔案同時上傳
- **用戶體驗**：即時進度回饋

### 適用場景：
- ✅ 大檔案上傳（影片、高解析度圖片）
- ✅ 批量檔案上傳
- ✅ 網路不穩定環境（支援重試）
- ✅ 高並發上傳需求

## 🧪 測試建議

### 1. 功能測試
```bash
# 測試上傳憑證生成
POST /api/upload/signed-url
{
  "files": [
    {
      "name": "test.jpg",
      "type": "image/jpeg", 
      "size": 1024000,
      "fileType": "image"
    }
  ]
}

# 測試檔案驗證
POST /api/upload/verify
{
  "files": [
    {
      "fileName": "images/1234567890-userId-abc123.jpg",
      "publicUrl": "https://storage.googleapis.com/bucket/file.jpg",
      "originalName": "test.jpg",
      "mimeType": "image/jpeg",
      "fileType": "image"
    }
  ]
}
```

### 2. 壓力測試
- 同時上傳多個大檔案
- 網路中斷恢復測試
- 檔案大小限制測試
- 檔案類型驗證測試

### 3. 安全測試
- 未認證用戶嘗試獲取憑證
- 過期 Signed URL 測試
- 惡意檔案類型測試
- 檔案大小超限測試

## 🔮 未來擴展

1. **斷點續傳**：支援大檔案分片上傳
2. **CDN 加速**：整合 GCP CDN 提升全球訪問速度
3. **智能壓縮**：根據網路狀況動態調整壓縮參數
4. **批量操作**：支援資料夾拖拽上傳
5. **進度持久化**：頁面刷新後恢復上傳進度

## 📝 使用說明

現在用戶上傳檔案時：

1. **選擇檔案** → 自動檔案驗證
2. **壓縮處理** → 顯示壓縮進度
3. **直接上傳** → 顯示上傳進度  
4. **發布貼文** → 只傳送 URL

整個過程更快、更穩定、用戶體驗更佳！
