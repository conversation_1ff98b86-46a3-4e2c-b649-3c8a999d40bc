version: '3.8'

services:
  # 主應用（Nginx + Node.js）
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sotime-app
    restart: unless-stopped
    ports:
      - "8888:6666"
    environment:
      - NODE_ENV=production
      - PORT=5003
      - MONGO_URI=***************************************************************************
      - JWT_SECRET=${JWT_SECRET:-sayhong}
      - GCP_PROJECT_ID=ccw-dev
      - GCP_BUCKET_NAME=liveshowsotime
      - GCP_KEY_FILE=config/ccw-dev-81e940b67f37.json
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - sotime-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6666/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis (可選，用於會話存儲或緩存)
  redis:
    image: redis:7-alpine
    container_name: sotime-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - sotime-network
    command: redis-server --appendonly yes

volumes:
  redis_data:
    driver: local

networks:
  sotime-network:
    driver: bridge
