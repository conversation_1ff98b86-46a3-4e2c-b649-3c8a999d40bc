# SSL 證書配置指南

## 概述
本文檔說明如何為 `liveshow.sotime.app` 域名配置 SSL 證書。

## 方法一：使用 Let's Encrypt (推薦)

### 1. 安裝 Certbot
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install certbot python3-certbot-nginx
```

### 2. 獲取 SSL 證書
```bash
# 停止當前的 nginx 服務
sudo systemctl stop nginx

# 獲取證書
sudo certbot certonly --standalone -d liveshow.sotime.app

# 或者如果 nginx 正在運行
sudo certbot --nginx -d liveshow.sotime.app
```

### 3. 更新 nginx 配置
證書文件通常位於：
- 證書文件：`/etc/letsencrypt/live/liveshow.sotime.app/fullchain.pem`
- 私鑰文件：`/etc/letsencrypt/live/liveshow.sotime.app/privkey.pem`

在 `nginx.prod.conf` 中取消註釋並更新 SSL 配置：
```nginx
ssl_certificate /etc/letsencrypt/live/liveshow.sotime.app/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/liveshow.sotime.app/privkey.pem;
```

### 4. 設置自動續期
```bash
# 測試續期
sudo certbot renew --dry-run

# 設置 cron 任務自動續期
sudo crontab -e
# 添加以下行：
0 12 * * * /usr/bin/certbot renew --quiet
```

## 方法二：使用自簽名證書（僅用於測試）

### 1. 生成自簽名證書
```bash
# 創建 SSL 目錄
sudo mkdir -p /etc/nginx/ssl

# 生成私鑰
sudo openssl genrsa -out /etc/nginx/ssl/liveshow.sotime.app.key 2048

# 生成證書
sudo openssl req -new -x509 -key /etc/nginx/ssl/liveshow.sotime.app.key \
    -out /etc/nginx/ssl/liveshow.sotime.app.crt -days 365 \
    -subj "/C=TW/ST=Taiwan/L=Taipei/O=SoTime/CN=liveshow.sotime.app"
```

### 2. 更新 nginx 配置
在 `nginx.prod.conf` 中取消註釋並更新：
```nginx
ssl_certificate /etc/nginx/ssl/liveshow.sotime.app.crt;
ssl_certificate_key /etc/nginx/ssl/liveshow.sotime.app.key;
```

## 方法三：使用雲服務提供商的 SSL

### Cloudflare
1. 在 Cloudflare 中添加域名
2. 啟用 SSL/TLS 加密
3. 設置 SSL/TLS 加密模式為 "Full" 或 "Full (strict)"
4. 使用 Cloudflare 的 Origin CA 證書

### AWS Certificate Manager
1. 在 AWS ACM 中申請證書
2. 驗證域名所有權
3. 在 ALB 或 CloudFront 中使用證書

## Docker 部署配置

### 1. 更新 docker-compose.yml
```yaml
services:
  app:
    # ... 其他配置
    ports:
      - "80:80"
      - "443:443"
      - "6666:6666"  # 開發用
    volumes:
      - uploads_data:/app/uploads
      - ./logs:/var/log/nginx
      - /etc/letsencrypt:/etc/letsencrypt:ro  # SSL 證書
      - ./nginx.prod.conf:/etc/nginx/nginx.conf  # 使用生產配置
```

### 2. 更新 Dockerfile
確保 Dockerfile 複製正確的 nginx 配置：
```dockerfile
# 複製 nginx 配置
COPY nginx.prod.conf /etc/nginx/nginx.conf
```

## 域名 DNS 配置

確保域名 `liveshow.sotime.app` 的 DNS 記錄指向您的服務器：

```
A    liveshow.sotime.app    YOUR_SERVER_IP
```

## 防火牆配置

確保服務器防火牆允許 HTTP 和 HTTPS 流量：

```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 6666/tcp  # 開發用

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=6666/tcp
sudo firewall-cmd --reload
```

## 測試 SSL 配置

### 1. 檢查證書
```bash
# 檢查證書詳情
openssl x509 -in /etc/letsencrypt/live/liveshow.sotime.app/fullchain.pem -text -noout

# 測試 SSL 連接
openssl s_client -connect liveshow.sotime.app:443
```

### 2. 在線測試工具
- SSL Labs: https://www.ssllabs.com/ssltest/
- SSL Checker: https://www.sslchecker.com/

## 故障排除

### 常見問題
1. **證書路徑錯誤**：確保 nginx 配置中的證書路徑正確
2. **權限問題**：確保 nginx 進程有讀取證書文件的權限
3. **防火牆阻擋**：確保 80 和 443 端口開放
4. **DNS 解析**：確保域名正確解析到服務器 IP

### 日誌檢查
```bash
# 檢查 nginx 錯誤日誌
sudo tail -f /var/log/nginx/error.log

# 檢查 certbot 日誌
sudo tail -f /var/log/letsencrypt/letsencrypt.log
```
