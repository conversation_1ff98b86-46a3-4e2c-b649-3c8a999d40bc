<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
<div class="streamer-profile-page bg-gray-900 min-h-screen pb-16">
<!-- 頂部導航欄 -->
<div class="nav-bar fixed top-0 w-full bg-gray-800 shadow-md z-50 px-4 py-3 flex items-center justify-between">
<a href="https://readdy.ai/home/<USER>/cf8ca966-b07a-4694-b08a-f370443026f1" data-readdy="true" class="text-gray-300 flex items-center justify-center cursor-pointer !rounded-button">
<i class="fas fa-arrow-left text-lg"></i>
</a>
<div class="flex-1 flex justify-center">
<span class="text-lg font-bold text-white">主播資訊</span>
</div>
<button class="text-gray-300 cursor-pointer !rounded-button">
<i class="fas fa-share-alt text-lg"></i>
</button>
</div>
<!-- 主內容區域 -->
<div class="content-area pt-14">
<!-- 主播基本資訊卡片 -->
<div class="streamer-info-card relative">
<!-- 背景圖 -->
<div class="bg-image h-44 w-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=Dreamy%20studio%20background%20for%20female%20streamer%20with%20soft%20gradient%20lighting%2C%20professional%20streaming%20equipment%20visible%2C%20musical%20instruments%2C%20cozy%20atmosphere%2C%20high%20quality%20photography%2C%20shallow%20depth%20of%20field%2C%20warm%20color%20palette%2C%20elegant%20and%20artistic%20setup&width=800&height=300&seq=101&orientation=landscape" alt="背景圖" class="w-full h-full object-cover object-top">
<div class="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900"></div>
</div>
<!-- 主播資訊 -->
<div class="streamer-details px-4 relative -mt-16">
<div class="flex items-end">
<div class="avatar-container relative">
<div class="avatar-wrapper rounded-full border-4 border-gray-800 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=Asian%20female%20streamer%20portrait%2C%20professional%20headshot%2C%20friendly%20smile%2C%20subtle%20makeup%2C%20clean%20background%2C%20high%20quality%20portrait%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20streaming%20setup%20visible&width=200&height=200&seq=5&orientation=squarish" alt="小雪" class="w-28 h-28 object-cover">
</div>
<div class="status-badge absolute bottom-1 right-1 bg-green-500 w-5 h-5 rounded-full border-2 border-gray-800 flex items-center justify-center">
<i class="fas fa-circle text-xs"></i>
</div>
</div>
<div class="ml-4 flex-1 mb-2">
<div class="flex justify-between items-center">
<h1 class="text-xl font-bold text-white">小雪</h1>
<span class="px-2 py-0.5 bg-pink-600 text-white text-xs rounded-full">在線</span>
</div>
<p class="text-sm text-gray-400 mt-1">ID: snowflake_1025</p>
</div>
</div>
<!-- 關注按鈕 -->
<div class="follow-btn-container mt-3 flex justify-between">
<button
:class="[
'py-2 px-6 rounded-full font-medium flex items-center justify-center cursor-pointer !rounded-button flex-1 mr-2',
isFollowing ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white' : 'bg-gray-700 text-gray-300'
]"
@click="toggleFollow"
>
<i :class="['mr-2', isFollowing ? 'fas fa-heart' : 'far fa-heart']"></i>
<span>{{ isFollowing ? '已關注' : '關注' }}</span>
</button>
<!-- 取消關注確認彈窗 -->
<div v-if="showUnfollowDialog" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
<div class="bg-gray-800 rounded-lg p-4 w-[280px] mx-4">
<h3 class="text-white text-center mb-3">確定要取消關注小雪嗎？</h3>
<div class="flex justify-center space-x-3">
<button
class="px-4 py-2 bg-gray-700 text-gray-300 rounded-full text-sm cursor-pointer !rounded-button"
@click="showUnfollowDialog = false"
>
取消
</button>
<button
class="px-4 py-2 bg-red-600 text-white rounded-full text-sm cursor-pointer !rounded-button"
@click="confirmUnfollow"
>
確認取消
</button>
</div>
</div>
</div>
<button
class="bg-gray-700 text-white py-2 px-4 rounded-full font-medium flex items-center justify-center cursor-pointer !rounded-button"
@click="showChatDialog = true"
>
<i class="fas fa-comment-dots"></i>
</button>
<!-- Private Message Dialog -->
<div v-if="showChatDialog" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
<div class="bg-gray-800 rounded-lg w-[320px] max-h-[80vh] flex flex-col">
<!-- Dialog Header -->
<div class="p-4 border-b border-gray-700 flex items-center justify-between">
<div class="flex items-center">
<img src="https://readdy.ai/api/search-image?query=Asian%20female%20streamer%20portrait%2C%20professional%20headshot%2C%20friendly%20smile%2C%20subtle%20makeup%2C%20clean%20background%2C%20high%20quality%20portrait%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20streaming%20setup%20visible&width=100&height=100&seq=119&orientation=squarish"
alt="小雪"
class="w-8 h-8 rounded-full object-cover mr-2"
>
<span class="text-white font-medium">小雪</span>
</div>
<button
class="text-gray-400 hover:text-white cursor-pointer !rounded-button"
@click="showChatDialog = false"
>
<i class="fas fa-times"></i>
</button>
</div>
<!-- Chat History -->
<div class="flex-1 overflow-y-auto p-4 space-y-4">
<div v-for="message in chatHistory" :key="message.id"
:class="['flex', message.isSelf ? 'justify-end' : 'justify-start']">
<div :class="['max-w-[70%]', message.isSelf ? 'items-end' : 'items-start']">
<div class="flex items-end gap-2" :class="[message.isSelf ? 'flex-row-reverse' : '']">
<img :src="message.avatar" :alt="message.name" class="w-6 h-6 rounded-full object-cover">
<div :class="[
'px-3 py-2 rounded-lg break-all',
message.isSelf ? 'bg-pink-600 text-white' : 'bg-gray-700 text-white'
]">
<p class="text-sm">{{message.content}}</p>
</div>
</div>
<span class="text-xs text-gray-500 mt-1 block" :class="[message.isSelf ? 'text-right' : '']">
{{formatTimeAgo(message.time)}}
</span>
</div>
</div>
</div>
<!-- Input Area -->
<div class="p-4 border-t border-gray-700">
<div class="flex items-center gap-2">
<div class="relative flex-1">
<input
v-model="newMessage"
type="text"
placeholder="發送訊息..."
class="w-full pl-4 pr-10 py-2 bg-gray-700 rounded-full text-sm text-white border-none focus:outline-none"
@keyup.enter="sendMessage"
>
<button
class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer !rounded-button"
@click="toggleEmoji"
>
<i class="far fa-smile"></i>
</button>
</div>
<button
class="bg-pink-600 text-white p-2 rounded-full cursor-pointer !rounded-button"
@click="sendMessage"
>
<i class="fas fa-paper-plane"></i>
</button>
</div>
</div>
</div>
</div>
<button
class="ml-2 bg-gray-700 text-white py-2 px-4 rounded-full font-medium flex items-center justify-center cursor-pointer !rounded-button"
@click="showGiftDialog = true"
>
<i class="fas fa-gift"></i>
</button>
<!-- Gift Dialog -->
<div v-if="showGiftDialog" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
<div class="bg-gray-800 rounded-lg w-[320px] max-h-[90vh] overflow-hidden">
<div class="p-4 border-b border-gray-700">
<h3 class="text-white text-center font-medium">贈送禮物給小雪</h3>
</div>
<div class="p-4 overflow-y-auto max-h-[60vh]">
<div class="grid grid-cols-4 gap-3">
<div
v-for="gift in gifts"
:key="gift.id"
:class="['gift-item p-2 rounded-lg cursor-pointer flex flex-col items-center',
selectedGift?.id === gift.id ? 'bg-pink-600/30 ring-2 ring-pink-500' : 'bg-gray-700/50']"
@click="selectGift(gift)"
>
<i :class="['text-2xl mb-1', gift.icon]"></i>
<span class="text-white text-xs">{{gift.name}}</span>
<span class="text-pink-400 text-xs mt-1">{{gift.price}}點</span>
</div>
</div>
</div>
<div class="p-4 border-t border-gray-700">
<div class="flex items-center justify-between mb-4">
<span class="text-gray-400 text-sm">數量</span>
<div class="flex items-center">
<button
class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white cursor-pointer !rounded-button"
@click="giftCount > 1 && giftCount--"
>
<i class="fas fa-minus text-sm"></i>
</button>
<span class="mx-4 text-white">{{giftCount}}</span>
<button
class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white cursor-pointer !rounded-button"
@click="giftCount++"
>
<i class="fas fa-plus text-sm"></i>
</button>
</div>
</div>
<div class="flex space-x-3">
<button
class="flex-1 py-2 bg-gray-700 text-gray-300 rounded-full text-sm cursor-pointer !rounded-button"
@click="showGiftDialog = false"
>
取消
</button>
<button
class="flex-1 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-full text-sm cursor-pointer !rounded-button"
@click="sendGift"
:disabled="!selectedGift"
>
贈送
</button>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 互動數據統計 -->
<div class="stats-container px-4 mt-4 grid grid-cols-4 gap-2">
<div class="stat-item bg-gray-800 rounded-lg p-3 flex flex-col items-center">
<span class="text-lg font-bold text-white">152萬</span>
<span class="text-xs text-gray-400 mt-1">總觀看</span>
</div>
<div class="stat-item bg-gray-800 rounded-lg p-3 flex flex-col items-center">
<span class="text-lg font-bold text-white">28.5萬</span>
<span class="text-xs text-gray-400 mt-1">粉絲</span>
</div>
<div class="stat-item bg-gray-800 rounded-lg p-3 flex flex-col items-center">
<span class="text-lg font-bold text-white">1,246</span>
<span class="text-xs text-gray-400 mt-1">禮物</span>
</div>
<div class="stat-item bg-gray-800 rounded-lg p-3 flex flex-col items-center">
<span class="text-lg font-bold text-white">86%</span>
<span class="text-xs text-gray-400 mt-1">互動率</span>
</div>
</div>
<!-- 主播簡介 -->
<div class="bio-container px-4 mt-4">
<div class="bg-gray-800 rounded-lg p-4">
<div class="flex justify-between items-center mb-2">
<h3 class="text-white font-medium">主播簡介</h3>
<button @click="toggleBioExpand" class="text-gray-400 cursor-pointer !rounded-button">
<i :class="['fas', bioExpanded ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
</button>
</div>
<p :class="['text-gray-300 text-sm', bioExpanded ? '' : 'line-clamp-3']">
嗨！我是小雪，一位熱愛音樂與遊戲的直播主。平時喜歡彈吉他、唱歌，也會直播一些熱門遊戲如《原神》、《英雄聯盟》等。每週五晚上會有固定的音樂直播，歡迎大家來點歌！我也很喜歡與觀眾互動，分享生活趣事，希望能帶給大家歡樂與放鬆的時光。感謝各位的支持與陪伴！
</p>
<div class="mt-3 flex flex-wrap gap-2" v-if="bioExpanded">
<span class="px-2 py-1 bg-gray-700 text-pink-400 text-xs rounded-full">音樂</span>
<span class="px-2 py-1 bg-gray-700 text-blue-400 text-xs rounded-full">遊戲</span>
<span class="px-2 py-1 bg-gray-700 text-purple-400 text-xs rounded-full">聊天</span>
<span class="px-2 py-1 bg-gray-700 text-green-400 text-xs rounded-full">才藝</span>
</div>
<div class="mt-3" v-if="bioExpanded">
<h4 class="text-white text-sm font-medium mb-2">直播時間表</h4>
<div class="grid grid-cols-2 gap-2 text-xs">
<div class="flex items-center">
<i class="fas fa-calendar-day text-gray-400 mr-2"></i>
<span class="text-gray-300">週一、三、五 20:00-23:00</span>
</div>
<div class="flex items-center">
<i class="fas fa-music text-gray-400 mr-2"></i>
<span class="text-gray-300">週五 音樂專場</span>
</div>
<div class="flex items-center">
<i class="fas fa-gamepad text-gray-400 mr-2"></i>
<span class="text-gray-300">週六 遊戲馬拉松 14:00-22:00</span>
</div>
<div class="flex items-center">
<i class="fas fa-comment text-gray-400 mr-2"></i>
<span class="text-gray-300">週日 閒聊互動 19:00-21:00</span>
</div>
</div>
</div>
</div>
</div>
<!-- 直播內容分類 -->
<div class="category-tabs sticky top-14 bg-gray-800 z-40 border-b border-gray-700 shadow-sm mt-4">
<div class="tabs-container overflow-x-auto">
<div class="tabs-wrapper flex px-2">
<button
v-for="(tab, index) in tabs"
:key="index"
:class="['tab-item px-4 py-3 text-sm font-medium whitespace-nowrap cursor-pointer',
activeTab === tab.id ? 'text-pink-500 border-b-2 border-pink-500' : 'text-gray-400']"
@click="setActiveTab(tab.id)"
>
{{ tab.name }}
</button>
</div>
</div>
</div>
<!-- 直播內容區域 -->
<div class="content-container px-4 mt-3">
<!-- 全部直播 -->
<div v-if="activeTab === 'all'" class="live-rooms-grid">
<div class="current-live mb-4" v-if="currentLive">
<h3 class="text-white font-medium mb-2 flex items-center">
<span class="inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
正在直播
</h3>
<div class="live-room-card bg-gray-800 rounded-lg overflow-hidden shadow-md cursor-pointer">
<div class="relative">
<img :src="currentLive.cover" alt="直播封面" class="w-full aspect-video object-cover">
<div class="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
<i class="fas fa-circle text-xs mr-1 animate-pulse"></i>
<span>直播中</span>
</div>
<div class="absolute top-2 left-2 bg-black/60 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
<i class="fas fa-eye mr-1 text-xs"></i>
<span>{{ formatNumber(currentLive.viewers) }}</span>
</div>
<div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-12"></div>
</div>
<div class="p-3">
<h4 class="text-white font-medium">{{ currentLive.title }}</h4>
<div class="flex items-center justify-between mt-2">
<div class="flex items-center">
<span class="text-xs text-gray-400">{{ formatTime(currentLive.startTime) }} 開始</span>
<span class="mx-2 text-gray-600">•</span>
<span class="text-xs text-gray-400">已直播 {{ getLiveDuration(currentLive.startTime) }}</span>
</div>
<div class="flex items-center">
<span v-for="(tag, index) in currentLive.tags" :key="index" class="ml-1 px-1.5 py-0.5 bg-pink-900/50 text-pink-400 text-xs rounded">{{ tag }}</span>
</div>
</div>
</div>
</div>
</div>
<div class="upcoming-lives mb-4" v-if="upcomingLives.length > 0">
<h3 class="text-white font-medium mb-2">即將開播</h3>
<div class="grid grid-cols-1 gap-3">
<div
v-for="live in upcomingLives"
:key="live.id"
class="upcoming-live-card bg-gray-800 rounded-lg p-3 flex items-center"
>
<div class="w-20 h-20 rounded-lg overflow-hidden mr-3">
<img :src="live.cover" alt="直播預告" class="w-full h-full object-cover">
</div>
<div class="flex-1">
<h4 class="text-white text-sm font-medium line-clamp-1">{{ live.title }}</h4>
<div class="flex items-center mt-1">
<i class="far fa-clock text-gray-400 text-xs mr-1"></i>
<span class="text-xs text-gray-400">{{ formatDate(live.scheduledTime) }}</span>
</div>
<div class="flex items-center mt-1">
<span v-for="(tag, index) in live.tags" :key="index" class="mr-1 px-1.5 py-0.5 bg-gray-700 text-gray-300 text-xs rounded">{{ tag }}</span>
</div>
</div>
<button
:class="['ml-2 px-3 py-1 text-xs rounded-full cursor-pointer !rounded-button',
hasReminder ? 'bg-pink-600 text-white' : 'bg-gray-700 text-gray-300']"
@click="toggleReminderDialog"
>
<i :class="['mr-1', hasReminder ? 'fas fa-bell' : 'far fa-bell']"></i>
{{ hasReminder ? '已設提醒' : '提醒' }}
</button>
<!-- 提醒設置彈窗 -->
<div v-if="showReminderDialog" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
<div class="bg-gray-800 rounded-lg p-4 w-[300px] mx-4">
<h3 class="text-white text-center mb-4">設置開播提醒</h3>
<div class="mb-4">
<h4 class="text-gray-400 text-sm mb-2">提醒方式</h4>
<div class="space-y-2">
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderMethod" value="app" class="mr-2">
APP 通知
</label>
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderMethod" value="sms" class="mr-2">
簡訊通知
</label>
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderMethod" value="email" class="mr-2">
電子郵件
</label>
</div>
</div>
<div class="mb-6">
<h4 class="text-gray-400 text-sm mb-2">提醒時間</h4>
<div class="space-y-2">
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderTime" value="15" class="mr-2">
開播前 15 分鐘
</label>
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderTime" value="30" class="mr-2">
開播前 30 分鐘
</label>
<label class="flex items-center text-white text-sm cursor-pointer">
<input type="radio" v-model="reminderTime" value="60" class="mr-2">
開播前 1 小時
</label>
</div>
</div>
<div class="flex justify-center space-x-3">
<button
class="px-4 py-2 bg-gray-700 text-gray-300 rounded-full text-sm cursor-pointer !rounded-button"
@click="showReminderDialog = false"
>
取消
</button>
<button
class="px-4 py-2 bg-pink-600 text-white rounded-full text-sm cursor-pointer !rounded-button"
@click="setReminder"
>
確認
</button>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="past-lives">
<h3 class="text-white font-medium mb-2">歷史直播</h3>
<div class="grid grid-cols-2 gap-3">
<div
v-for="live in pastLives"
:key="live.id"
class="past-live-card bg-gray-800 rounded-lg overflow-hidden shadow-md cursor-pointer"
>
<div class="relative">
<img :src="live.cover" alt="歷史直播" class="w-full aspect-video object-cover">
<div class="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-0.5 rounded-full">
{{ live.duration }}
</div>
</div>
<div class="p-2">
<h4 class="text-sm text-white line-clamp-2">{{ live.title }}</h4>
<div class="flex items-center justify-between mt-1">
<span class="text-xs text-gray-400">{{ formatNumber(live.views) }} 次觀看</span>
<span class="text-xs text-gray-400">{{ formatTimeAgo(live.endTime) }}</span>
</div>
</div>
</div>
</div>
<div class="loading-more py-4 flex justify-center">
<button
:class="[
'px-4 py-2 rounded-full text-sm !rounded-button flex items-center justify-center min-w-[120px]',
hasMoreContent ? 'bg-gray-800 text-gray-300 cursor-pointer' : 'bg-gray-700 text-gray-500 cursor-not-allowed'
]"
@click="loadMoreHighlights"
:disabled="isLoadingMore || !hasMoreContent"
>
<template v-if="isLoadingMore">
<i class="fas fa-spinner fa-spin mr-2"></i>
載入中...
</template>
<template v-else-if="!hasMoreContent">
已顯示全部內容
</template>
<template v-else>
載入更多
</template>
</button>
</div>
</div>
</div>
<!-- 精彩回放 -->
<div v-if="activeTab === 'highlights'" class="highlights-container">
<div class="grid grid-cols-1 gap-4">
<div
v-for="highlight in highlights"
:key="highlight.id"
class="highlight-card bg-gray-800 rounded-lg overflow-hidden shadow-md cursor-pointer"
>
<div class="relative">
<a href="https://readdy.ai/home/<USER>/7ec1a93c-6bee-4295-9057-381e7c822f3c" data-readdy="true">
<img :src="highlight.cover" alt="精彩回放" class="w-full aspect-video object-cover">
</a>
<div class="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-0.5 rounded-full">
{{ highlight.duration }}
</div>
<div class="absolute top-2 right-2 bg-purple-600/80 text-white text-xs px-2 py-0.5 rounded-full">
精選
</div>
</div>
<div class="p-3">
<h4 class="text-white font-medium">{{ highlight.title }}</h4>
<p class="text-sm text-gray-400 mt-1 line-clamp-2">{{ highlight.description }}</p>
<div class="flex items-center justify-between mt-2">
<div class="flex items-center">
<span class="text-xs text-gray-400">{{ formatNumber(highlight.views) }} 次觀看</span>
<span class="mx-2 text-gray-600">•</span>
<span class="text-xs text-gray-400">{{ formatTimeAgo(highlight.date) }}</span>
</div>
<div class="flex items-center">
<button class="text-gray-400 mr-2 cursor-pointer !rounded-button">
<i class="far fa-heart"></i>
</button>
<button class="text-gray-400 cursor-pointer !rounded-button">
<i class="far fa-share-square"></i>
</button>
</div>
</div>
</div>
</div>
</div>
<div class="loading-more py-4 flex justify-center">
<button
:class="[
'px-4 py-2 rounded-full text-sm !rounded-button flex items-center justify-center min-w-[120px]',
hasMoreContent ? 'bg-gray-800 text-gray-300 cursor-pointer' : 'bg-gray-700 text-gray-500 cursor-not-allowed'
]"
@click="loadMoreHighlights"
:disabled="isLoadingMore || !hasMoreContent"
>
<template v-if="isLoadingMore">
<i class="fas fa-spinner fa-spin mr-2"></i>
載入中...
</template>
<template v-else-if="!hasMoreContent">
已顯示全部內容
</template>
<template v-else>
載入更多
</template>
</button>
</div>
</div>
<!-- 互動留言 -->
<div v-if="activeTab === 'comments'" class="comments-container">
<div class="comment-input mb-4">
<div class="bg-gray-800 rounded-lg p-3">
<div class="flex items-center">
<img src="https://readdy.ai/api/search-image?query=Generic%20user%20avatar%2C%20minimalist%20design%2C%20neutral%20expression%2C%20clean%20background%2C%20high%20quality%20portrait%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20style&width=100&height=100&seq=103&orientation=squarish" alt="用戶頭像" class="w-10 h-10 rounded-full object-cover mr-3">
<div class="flex-1 relative">
<input
type="text"
placeholder="留言給小雪..."
class="w-full pl-4 pr-10 py-2 bg-gray-700 rounded-full text-sm focus:outline-none border-none text-white"
>
<button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer !rounded-button">
<i class="far fa-paper-plane"></i>
</button>
</div>
</div>
</div>
</div>
<div class="comments-list">
<div class="flex justify-between items-center mb-3">
<h3 class="text-white font-medium">留言板</h3>
<div class="flex items-center">
<button :class="['px-2 py-1 text-xs rounded-full mr-1 cursor-pointer !rounded-button', commentSort === 'latest' ? 'bg-pink-600 text-white' : 'bg-gray-700 text-gray-300']" @click="commentSort = 'latest'">
最新
</button>
<button :class="['px-2 py-1 text-xs rounded-full cursor-pointer !rounded-button', commentSort === 'popular' ? 'bg-pink-600 text-white' : 'bg-gray-700 text-gray-300']" @click="commentSort = 'popular'">
熱門
</button>
</div>
</div>
<div class="space-y-4">
<div
v-for="comment in sortedComments"
:key="comment.id"
class="comment-item bg-gray-800 rounded-lg p-3"
>
<div class="flex">
<img :src="comment.userAvatar" alt="用戶頭像" class="w-10 h-10 rounded-full object-cover">
<div class="ml-3 flex-1">
<div class="flex items-center justify-between">
<div>
<span class="text-white text-sm font-medium">{{ comment.userName }}</span>
<span v-if="comment.isVip" class="ml-1 px-1 py-0.5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-xs rounded">VIP</span>
</div>
<span class="text-xs text-gray-500">{{ formatTimeAgo(comment.time) }}</span>
</div>
<p class="text-gray-300 text-sm mt-1">{{ comment.content }}</p>
<div class="flex items-center mt-2">
<button
:class="[
'flex items-center text-xs mr-3 cursor-pointer !rounded-button transition-all duration-200',
comment.isLiked ? 'text-pink-500' : 'text-gray-400'
]"
@click="toggleLike(comment.id)"
>
<i :class="[
'mr-1 transform transition-transform duration-200',
comment.isLiked ? 'fas fa-heart scale-110' : 'far fa-heart'
]"></i>
<span>{{ comment.likes }}</span>
</button>
<button 
class="text-xs text-gray-400 cursor-pointer !rounded-button"
@click="toggleReplyInput(comment.id)"
>
<i class="far fa-comment mr-1"></i>
回覆
</button>
</div>
<!-- Reply Input Area -->
<div v-if="comment.showReplyInput" class="mt-3 ml-8">
<div class="flex items-center gap-2">
<div class="relative flex-1">
<input
v-model="comment.replyContent"
type="text"
:placeholder="`回覆 ${comment.userName}...`"
class="w-full pl-4 pr-10 py-2 bg-gray-700 rounded-full text-sm text-white border-none focus:outline-none"
@keyup.enter="submitReply(comment.id)"
>
</div>
<button
class="bg-pink-600 text-white p-2 rounded-full cursor-pointer !rounded-button"
@click="submitReply(comment.id)"
>
<i class="fas fa-paper-plane"></i>
</button>
<button
class="bg-gray-700 text-gray-300 p-2 rounded-full cursor-pointer !rounded-button"
@click="toggleReplyInput(comment.id)"
>
<i class="fas fa-times"></i>
</button>
</div>
</div>
<!-- Replies List -->
<div v-if="comment.replies && comment.replies.length > 0" class="mt-3 ml-8 space-y-3">
<div v-for="reply in comment.replies" :key="reply.id" class="bg-gray-800/50 rounded-lg p-3">
<div class="flex">
<img :src="reply.userAvatar" alt="用戶頭像" class="w-8 h-8 rounded-full object-cover">
<div class="ml-3 flex-1">
<div class="flex items-center justify-between">
<span class="text-white text-sm">{{ reply.userName }}</span>
<span class="text-xs text-gray-500">{{ formatTimeAgo(reply.time) }}</span>
</div>
<p class="text-gray-300 text-sm mt-1">{{ reply.content }}</p>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="loading-more py-4 flex justify-center">
<button class="px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm cursor-pointer !rounded-button">
載入更多留言
</button>
</div>
</div>
</div>
<!-- 關於主播 -->
<div v-if="activeTab === 'about'" class="about-container">
<div class="bg-gray-800 rounded-lg p-4 mb-4">
<h3 class="text-white font-medium mb-3">主播資料</h3>
<div class="space-y-3">
<div class="flex">
<div class="w-24 text-gray-400 text-sm">真實姓名</div>
<div class="text-white text-sm">林小雪</div>
</div>
<div class="flex">
<div class="w-24 text-gray-400 text-sm">年齡</div>
<div class="text-white text-sm">25 歲</div>
</div>
<div class="flex">
<div class="w-24 text-gray-400 text-sm">地區</div>
<div class="text-white text-sm">台北市</div>
</div>
<div class="flex">
<div class="w-24 text-gray-400 text-sm">開播日期</div>
<div class="text-white text-sm">2023 年 3 月 15 日</div>
</div>
<div class="flex">
<div class="w-24 text-gray-400 text-sm">語言</div>
<div class="text-white text-sm">中文、英文</div>
</div>
</div>
</div>
<div class="bg-gray-800 rounded-lg p-4 mb-4">
<h3 class="text-white font-medium mb-3">成就與獎項</h3>
<div class="space-y-3">
<div class="achievement-item flex items-center">
<div class="achievement-icon w-10 h-10 rounded-full bg-purple-900 flex items-center justify-center mr-3">
<i class="fas fa-trophy text-yellow-400"></i>
</div>
<div>
<h4 class="text-white text-sm font-medium">2024 年度最受歡迎音樂主播</h4>
<p class="text-gray-400 text-xs mt-0.5">2024 年 4 月獲得</p>
</div>
</div>
<div class="achievement-item flex items-center">
<div class="achievement-icon w-10 h-10 rounded-full bg-blue-900 flex items-center justify-center mr-3">
<i class="fas fa-award text-blue-400"></i>
</div>
<div>
<h4 class="text-white text-sm font-medium">突破 20 萬粉絲里程碑</h4>
<p class="text-gray-400 text-xs mt-0.5">2024 年 2 月達成</p>
</div>
</div>
<div class="achievement-item flex items-center">
<div class="achievement-icon w-10 h-10 rounded-full bg-pink-900 flex items-center justify-center mr-3">
<i class="fas fa-star text-pink-400"></i>
</div>
<div>
<h4 class="text-white text-sm font-medium">2023 年度最佳新人主播</h4>
<p class="text-gray-400 text-xs mt-0.5">2023 年 12 月獲得</p>
</div>
</div>
</div>
</div>
<div class="bg-gray-800 rounded-lg p-4 mb-4">
<h3 class="text-white font-medium mb-3">社群媒體</h3>
<div class="grid grid-cols-2 gap-3">
<a href="#" class="social-link bg-gray-700 rounded-lg p-3 flex items-center cursor-pointer">
<div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">
<i class="fab fa-facebook-f text-white"></i>
</div>
<span class="text-white text-sm">Facebook</span>
</a>
<a href="#" class="social-link bg-gray-700 rounded-lg p-3 flex items-center cursor-pointer">
<div class="w-8 h-8 rounded-full bg-pink-600 flex items-center justify-center mr-2">
<i class="fab fa-instagram text-white"></i>
</div>
<span class="text-white text-sm">Instagram</span>
</a>
<a href="#" class="social-link bg-gray-700 rounded-lg p-3 flex items-center cursor-pointer">
<div class="w-8 h-8 rounded-full bg-red-600 flex items-center justify-center mr-2">
<i class="fab fa-youtube text-white"></i>
</div>
<span class="text-white text-sm">YouTube</span>
</a>
<a href="#" class="social-link bg-gray-700 rounded-lg p-3 flex items-center cursor-pointer">
<div class="w-8 h-8 rounded-full bg-blue-400 flex items-center justify-center mr-2">
<i class="fab fa-twitter text-white"></i>
</div>
<span class="text-white text-sm">Twitter</span>
</a>
</div>
</div>
<div class="bg-gray-800 rounded-lg p-4">
<h3 class="text-white font-medium mb-3">直播設備</h3>
<div class="space-y-2 text-sm">
<div class="flex justify-between">
<span class="text-gray-400">麥克風</span>
<span class="text-white">Shure SM7B</span>
</div>
<div class="flex justify-between">
<span class="text-gray-400">攝影機</span>
<span class="text-white">Sony Alpha a7 III</span>
</div>
<div class="flex justify-between">
<span class="text-gray-400">電腦</span>
<span class="text-white">自組 RTX 3080 遊戲主機</span>
</div>
<div class="flex justify-between">
<span class="text-gray-400">音效卡</span>
<span class="text-white">GoXLR Mini</span>
</div>
<div class="flex justify-between">
<span class="text-gray-400">樂器</span>
<span class="text-white">Taylor 214ce 木吉他</span>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
const isFollowing = ref(true);
const showUnfollowDialog = ref(false);
const showGiftDialog = ref(false);
const selectedGift = ref(null);
const giftCount = ref(1);
const gifts = ref([
{ id: 1, name: '鑽石', icon: 'fas fa-gem', price: 100 },
{ id: 2, name: '花束', icon: 'fas fa-flower-daffodil', price: 50 },
{ id: 3, name: '皇冠', icon: 'fas fa-crown', price: 500 },
{ id: 4, name: '愛心', icon: 'fas fa-heart', price: 10 },
{ id: 5, name: '星星', icon: 'fas fa-star', price: 30 },
{ id: 6, name: '火箭', icon: 'fas fa-rocket', price: 1000 },
{ id: 7, name: '蛋糕', icon: 'fas fa-cake-candles', price: 80 },
{ id: 8, name: '麥克風', icon: 'fas fa-microphone', price: 200 }
]);
const selectGift = (gift) => {
selectedGift.value = gift;
};
const sendGift = () => {
if (!selectedGift.value) return;
// Here you can add the logic to process the gift sending
const giftData = {
gift: selectedGift.value,
count: giftCount.value,
timestamp: Date.now()
};
// Reset and close dialog
showGiftDialog.value = false;
selectedGift.value = null;
giftCount.value = 1;
// Show success message
showSuccessMessage(`成功贈送 ${giftCount.value} 個 ${selectedGift.value.name}`);
};
const showSuccessMessage = (message) => {
// Add your success message display logic here
console.log(message);
};
const toggleFollow = () => {
if (isFollowing.value) {
showUnfollowDialog.value = true;
} else {
isFollowing.value = true;
}
};
const confirmUnfollow = () => {
isFollowing.value = false;
showUnfollowDialog.value = false;
};
// 主播簡介展開狀態
const bioExpanded = ref(false);
const toggleBioExpand = () => {
bioExpanded.value = !bioExpanded.value;
};
// 分類標籤
const tabs = ref([
{ id: 'all', name: '全部直播' },
{ id: 'highlights', name: '精彩回放' },
{ id: 'comments', name: '互動留言' },
{ id: 'about', name: '關於主播' }
]);
const activeTab = ref('all');
const setActiveTab = (tabId: string) => {
activeTab.value = tabId;
};
// 留言排序
const commentSort = ref('latest');
// 格式化數字
const formatNumber = (num: number): string => {
if (num >= 10000) {
return (num / 10000).toFixed(1) + '萬';
}
return num.toString();
};
// 格式化時間
const formatTime = (timestamp: number): string => {
const date = new Date(timestamp);
const hours = date.getHours().toString().padStart(2, '0');
const minutes = date.getMinutes().toString().padStart(2, '0');
return `${hours}:${minutes}`;
};
// 格式化日期
const formatDate = (timestamp: number): string => {
const date = new Date(timestamp);
const month = (date.getMonth() + 1).toString().padStart(2, '0');
const day = date.getDate().toString().padStart(2, '0');
const hours = date.getHours().toString().padStart(2, '0');
const minutes = date.getMinutes().toString().padStart(2, '0');
return `${month}/${day} ${hours}:${minutes}`;
};
// 計算直播時長
const getLiveDuration = (startTime: number): string => {
const now = Date.now();
const diffInMinutes = Math.floor((now - startTime) / (1000 * 60));
if (diffInMinutes < 60) {
return `${diffInMinutes} 分鐘`;
} else {
const hours = Math.floor(diffInMinutes / 60);
const minutes = diffInMinutes % 60;
return `${hours} 小時 ${minutes} 分鐘`;
}
};
// 格式化相對時間
// 提醒相關狀態
const showReminderDialog = ref(false);
const reminderMethod = ref('app');
const reminderTime = ref('30');
const hasReminder = ref(false);
// 切換提醒彈窗
const toggleReminderDialog = () => {
if (!hasReminder.value) {
showReminderDialog.value = true;
} else {
// 如果已經設置了提醒，點擊則取消提醒
hasReminder.value = false;
}
};
// 設置提醒
const setReminder = () => {
hasReminder.value = true;
showReminderDialog.value = false;
// 這裡可以添加實際的提醒邏輯
const reminderData = {
method: reminderMethod.value,
time: reminderTime.value,
timestamp: Date.now()
};
console.log('設置提醒:', reminderData);
};
const formatTimeAgo = (timestamp: number): string => {
const now = Date.now();
const diffInSeconds = Math.floor((now - timestamp) / 1000);
if (diffInSeconds < 60) {
return `${diffInSeconds} 秒前`;
} else if (diffInSeconds < 3600) {
return `${Math.floor(diffInSeconds / 60)} 分鐘前`;
} else if (diffInSeconds < 86400) {
return `${Math.floor(diffInSeconds / 3600)} 小時前`;
} else if (diffInSeconds < 2592000) {
return `${Math.floor(diffInSeconds / 86400)} 天前`;
} else {
const date = new Date(timestamp);
const month = (date.getMonth() + 1).toString().padStart(2, '0');
const day = date.getDate().toString().padStart(2, '0');
return `${month}/${day}`;
}
};
// 當前直播
const currentLive = ref({
id: 1,
title: '週末音樂放鬆時光 ♪ 點歌互動中',
cover: 'https://readdy.ai/api/search-image?query=Female%20streamer%20playing%20acoustic%20guitar%20in%20cozy%20room%20setting%2C%20warm%20lighting%2C%20musical%20instruments%20visible%20in%20background%2C%20professional%20streaming%20setup%20with%20microphones%2C%20intimate%20atmosphere%2C%20high%20quality%20photography%2C%20shallow%20depth%20of%20field&width=800&height=450&seq=104&orientation=landscape',
viewers: 8742,
startTime: Date.now() - 3600000 * 2 - 900000, // 2小時30分鐘前
tags: ['音樂', '吉他']
});
// 即將開播
const upcomingLives = ref([
{
id: 2,
title: '【原神】新角色抽卡直播',
cover: 'https://readdy.ai/api/search-image?query=Genshin%20Impact%20gameplay%20screenshot%20showing%20anime-style%20character%20gacha%20pull%20moment%2C%20colorful%20special%20effects%2C%20high%20quality%20game%20graphics%2C%20professional%20streaming%20overlay%20visible&width=400&height=225&seq=105&orientation=landscape',
scheduledTime: Date.now() + 86400000, // 明天
tags: ['原神', '遊戲'],
hasReminder: false
},
{
id: 3,
title: '與粉絲互動時間 - 聊聊最近的生活',
cover: 'https://readdy.ai/api/search-image?query=Cozy%20streaming%20setup%20with%20soft%20lighting%2C%20comfortable%20chair%2C%20decorative%20elements%2C%20plants%2C%20and%20professional%20equipment%2C%20warm%20atmosphere%2C%20high%20quality%20indoor%20photography&width=400&height=225&seq=106&orientation=landscape',
scheduledTime: Date.now() + 172800000, // 後天
tags: ['閒聊', '互動']
}
]);
// 歷史直播
const pastLives = ref([
{
id: 4,
title: '【英雄聯盟】排位賽挑戰 - 一起衝上鑽石',
cover: 'https://readdy.ai/api/search-image?query=League%20of%20Legends%20gameplay%20screenshot%20showing%20intense%20team%20battle%20with%20special%20effects%2C%20high%20quality%20game%20graphics%2C%20vibrant%20colors%2C%20action%20moment%20captured%2C%20professional%20streaming%20overlay%20visible&width=400&height=225&seq=107&orientation=landscape',
views: 12583,
duration: '3:45:21',
endTime: Date.now() - 86400000 // 昨天
},
{
id: 5,
title: '翻唱歌曲大集合 - 華語經典',
cover: 'https://readdy.ai/api/search-image?query=Female%20vocalist%20performing%20with%20microphone%20in%20cozy%20studio%20setting%2C%20warm%20lighting%2C%20musical%20equipment%20visible%2C%20professional%20streaming%20setup%2C%20intimate%20atmosphere%2C%20high%20quality%20photography&width=400&height=225&seq=108&orientation=landscape',
views: 15762,
duration: '2:15:08',
endTime: Date.now() - 172800000 // 前天
},
{
id: 6,
title: '【動森】打造夢想小島 - 島嶼改造計劃',
cover: 'https://readdy.ai/api/search-image?query=Animal%20Crossing%20New%20Horizons%20gameplay%20screenshot%20showing%20colorful%20island%20design%20with%20cute%20characters%2C%20cozy%20village%20setting%2C%20high%20quality%20game%20graphics%2C%20professional%20streaming%20overlay&width=400&height=225&seq=109&orientation=landscape',
views: 9432,
duration: '4:02:15',
endTime: Date.now() - 259200000 // 3天前
},
{
id: 7,
title: '週末音樂放鬆時光 - 彈唱經典歌曲',
cover: 'https://readdy.ai/api/search-image?query=Female%20musician%20playing%20acoustic%20guitar%20in%20warm-lit%20room%2C%20cozy%20atmosphere%2C%20professional%20streaming%20setup%20with%20microphones%2C%20musical%20instruments%20in%20background%2C%20high%20quality%20photography&width=400&height=225&seq=110&orientation=landscape',
views: 18543,
duration: '3:30:42',
endTime: Date.now() - 345600000 // 4天前
}
]);
// 精彩回放
// Add new state variables
const isLoadingMore = ref(false);
const hasMoreContent = ref(true);
const currentPage = ref(1);
// Initial highlights data
const highlights = ref([
{
id: 8,
title: '小雪 2024 年度精彩時刻合集',
cover: 'https://readdy.ai/api/search-image?query=Female%20streamer%20compilation%20thumbnail%20showing%20multiple%20scenes%20with%20transitions%2C%20vibrant%20colors%2C%20professional%20editing%20style%2C%20high%20quality%20graphics%2C%20engaging%20visuals%2C%20montage%20style&width=800&height=450&seq=111&orientation=landscape',
description: '這是我 2024 年上半年的精彩時刻合集，包含遊戲高光、音樂表演、搞笑瞬間以及與粉絲的互動。感謝大家一直以來的支持！',
views: 32541,
duration: '15:32',
date: Date.now() - 604800000 // 一週前
},
{
id: 9,
title: '【原神】一發入魂！超狂十連抽全出金',
cover: 'https://readdy.ai/api/search-image?query=Genshin%20Impact%20gacha%20pull%20showing%20multiple%205-star%20character%20pulls%20in%20sequence%2C%20golden%20special%20effects%2C%20excited%20reaction%20overlay%2C%20high%20quality%20game%20graphics%2C%20professional%20streaming%20layout&width=800&height=450&seq=112&orientation=landscape',
description: '這可能是我玩原神以來最幸運的一次抽卡了！一次十連竟然出了兩個金角色，太不可思議了，大家一定要看看這個神奇的時刻！',
views: 28976,
duration: '8:45',
date: Date.now() - 1209600000 // 兩週前
},
{
id: 10,
title: '【翻唱】周杰倫《晴天》- 小雪吉他彈唱版',
cover: 'https://readdy.ai/api/search-image?query=Female%20vocalist%20playing%20acoustic%20guitar%20and%20singing%2C%20intimate%20close-up%20shot%2C%20warm%20lighting%2C%20professional%20audio%20setup%20visible%2C%20cozy%20studio%20environment%2C%20high%20quality%20music%20performance%20photography&width=800&height=450&seq=113&orientation=landscape',
description: '應大家的要求，這是我翻唱的周杰倫《晴天》，希望大家喜歡我的詮釋版本。如果喜歡請幫我分享出去，謝謝支持！',
views: 45321,
duration: '5:28',
date: Date.now() - 1814400000 // 三週前
}
]);
// 留言數據
// Add new refs and data
const showChatDialog = ref(false);
const newMessage = ref('');
const chatHistory = ref([
{
id: 1,
content: '你好！歡迎來到我的直播間，有什麼想和我聊的嗎？',
time: Date.now() - 3600000 * 24,
isSelf: false,
name: '小雪',
avatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20streamer%20portrait%2C%20professional%20headshot%2C%20friendly%20smile%2C%20subtle%20makeup%2C%20clean%20background%2C%20high%20quality%20portrait%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20streaming%20setup%20visible&width=100&height=100&seq=119&orientation=squarish'
},
{
id: 2,
content: '小雪姐姐好！我超喜歡你的歌聲，昨天的直播太棒了！',
time: Date.now() - 3600000 * 23,
isSelf: true,
name: '我',
avatar: 'https://readdy.ai/api/search-image?query=Generic%20user%20avatar%2C%20minimalist%20design%2C%20neutral%20expression%2C%20clean%20background%2C%20high%20quality%20portrait%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20style&width=100&height=100&seq=103&orientation=squarish'
},
{
id: 3,
content: '謝謝你的支持！很高興你喜歡我的表演 😊',
time: Date.now() - 3600000 * 22,
isSelf: false,
name: '小雪',
avatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20streamer%20portrait%2C%20professional%20headshot%2C%20friendly%20smile%2C%20subtle%20makeup%2C%20clean%20background%2C%20high%20quality%20portrait%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20streaming%20setup%20visible&width=100&height=100&seq=119&orientation=squarish'
}
]);
// Add new methods
const sendMessage = () => {
if (!newMessage.value.trim()) return;
chatHistory.value.push({
id: Date.now(),
content: newMessage.value,
time: Date.now(),
isSelf: true,
name: '我',
avatar: 'https://readdy.ai/api/search-image?query=Generic%20user%20avatar%2C%20minimalist%20design%2C%20neutral%20expression%2C%20clean%20background%2C%20high%20quality%20portrait%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20style&width=100&height=100&seq=103&orientation=squarish'
});
newMessage.value = '';
};
const toggleEmoji = () => {
// Emoji picker functionality can be added here
};
const comments = ref([
{
id: 1,
userName: '星空漫遊者',
userAvatar: 'https://readdy.ai/api/search-image?query=Asian%20male%20avatar%2C%20professional%20portrait%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features&width=100&height=100&seq=114&orientation=squarish',
content: '小雪的歌聲真的太好聽了！每次聽你彈吉他我都能放鬆心情，期待下次的音樂直播！',
time: Date.now() - 3600000, // 1小時前
likes: 42,
isVip: true,
isLiked: false,
replies: [],
showReplyInput: false,
replyContent: ''
},
{
id: 2,
userName: '遊戲達人',
userAvatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20avatar%20with%20gaming%20headset%2C%20professional%20portrait%2C%20friendly%20expression%2C%20clean%20background%2C%20high%20quality%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features&width=100&height=100&seq=115&orientation=squarish',
content: '昨天的英雄聯盟排位賽太精彩了！那個五殺真的讓我大開眼界，小雪技術越來越好了！',
time: Date.now() - 7200000, // 2小時前
likes: 38,
isVip: false
},
{
id: 3,
userName: '音樂愛好者',
userAvatar: 'https://readdy.ai/api/search-image?query=Asian%20male%20avatar%20with%20headphones%2C%20professional%20portrait%2C%20casual%20style%2C%20clean%20background%2C%20high%20quality%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features&width=100&height=100&seq=116&orientation=squarish',
content: '小雪姐姐能不能下次直播翻唱一下陳奕迅的《富士山下》？我超喜歡這首歌的！',
time: Date.now() - 10800000, // 3小時前
likes: 27,
isVip: false
},
{
id: 4,
userName: '夜貓子',
userAvatar: 'https://readdy.ai/api/search-image?query=Asian%20female%20avatar%2C%20professional%20portrait%2C%20stylish%20appearance%2C%20clean%20background%2C%20high%20quality%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features&width=100&height=100&seq=117&orientation=squarish',
content: '每次熬夜都會來看小雪的直播，感覺整個人都被治癒了。你的聲音真的很有魔力！',
time: Date.now() - 86400000, // 1天前
likes: 56,
isVip: true
},
{
id: 5,
userName: '雲玩家',
userAvatar: 'https://readdy.ai/api/search-image?query=Asian%20male%20avatar%2C%20professional%20portrait%2C%20glasses%2C%20clean%20background%2C%20high%20quality%20photography%2C%20well-lit%20face%2C%20clear%20facial%20features&width=100&height=100&seq=118&orientation=squarish',
content: '雖然我不太會玩原神，但是看小雪玩遊戲真的很有趣！解說很詳細，讓我這個新手也能看懂。',
time: Date.now() - 172800000, // 2天前
likes: 31,
isVip: false
}
]);
// 根據排序方式排序留言
// Add new method for like functionality
const toggleLike = (commentId: number) => {
const comment = comments.value.find(c => c.id === commentId);
if (comment) {
comment.isLiked = !comment.isLiked;
comment.likes += comment.isLiked ? 1 : -1;
}
};

const toggleReplyInput = (commentId: number) => {
const comment = comments.value.find(c => c.id === commentId);
if (comment) {
comment.showReplyInput = !comment.showReplyInput;
if (!comment.showReplyInput) {
comment.replyContent = '';
}
}
};

const submitReply = (commentId: number) => {
const comment = comments.value.find(c => c.id === commentId);
if (comment && comment.replyContent.trim()) {
if (!comment.replies) {
comment.replies = [];
}
comment.replies.push({
id: Date.now(),
userName: '我',
userAvatar: 'https://readdy.ai/api/search-image?query=Generic%20user%20avatar%2C%20minimalist%20design%2C%20neutral%20expression%2C%20clean%20background%2C%20high%20quality%20portrait%2C%20well-lit%20face%2C%20clear%20facial%20features%2C%20modern%20style&width=100&height=100&seq=103&orientation=squarish',
content: comment.replyContent,
time: Date.now()
});
comment.replyContent = '';
comment.showReplyInput = false;
}
};
const sortedComments = computed(() => {
if (commentSort.value === 'latest') {
return [...comments.value].sort((a, b) => b.time - a.time);
} else {
return [...comments.value].sort((a, b) => b.likes - a.likes);
}
});
</script>
<style scoped>
.tabs-container {
scrollbar-width: none;
-ms-overflow-style: none;
}
.tabs-container::-webkit-scrollbar {
display: none;
}
@keyframes pulse {
0% { opacity: 1; }
50% { opacity: 0.5; }
100% { opacity: 1; }
}
.animate-pulse {
animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.line-clamp-1 {
overflow: hidden;
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
}
.line-clamp-2 {
overflow: hidden;
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 2;
}
.line-clamp-3 {
overflow: hidden;
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 3;
}
</style>
