<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
<div class="live-streaming-app bg-gray-900 h-screen w-full relative overflow-hidden">
<!-- 直播視頻區域 -->
<div class="video-container relative w-full h-full" @click="handleBackgroundClick">
<img
:src="liveStreamUrl"
alt="直播畫面"
class="w-full h-full object-cover"
/>
<!-- 頂部信息欄 -->
<div class="absolute top-0 left-0 right-0 px-4 py-2 flex justify-between items-center bg-gradient-to-b from-black/70 to-transparent">
<!-- 主播信息 -->
<div class="flex items-center" @click.stop="showHostInfo = true">
<div class="relative">
<img :src="hostInfo.avatar" alt="主播頭像" class="w-10 h-10 rounded-full border-2 border-pink-500" />
<div class="absolute -bottom-1 -right-1 bg-red-500 text-white text-xs px-1 rounded-full">
直播中
</div>
</div>
<div class="ml-2 text-white">
<div class="flex items-center">
<span class="font-medium">{{ hostInfo.name }}</span>
<span class="ml-2 text-xs bg-gradient-to-r from-pink-500 to-purple-500 px-1.5 py-0.5 rounded-full">明星主播</span>
</div>
<div class="text-xs opacity-80">{{ onlineCount.toLocaleString() }} 人正在觀看</div>
</div>
</div>
<!-- Empty div to maintain layout structure -->
<div></div>
</div>
<!-- 觀眾排行榜預覽 -->
<div class="absolute right-0 top-2 flex flex-col items-end">
<div class="flex items-center">
<div class="flex overflow-x-auto hide-scrollbar px-2" style="max-width: 40vw">
<div class="flex-none flex items-center space-x-2">
<div v-for="(user, index) in rankingList.slice(0, 10)" :key="user.id"
class="flex-none cursor-pointer"
@click.stop="showUserInfo(user.id)">
<div class="relative">
<img :src="user.avatar" :alt="user.name" class="w-8 h-8 rounded-full border-2"
:class="[
index === 0 ? 'border-yellow-400' :
index === 1 ? 'border-gray-300' :
index === 2 ? 'border-orange-400' :
'border-white/30'
]">
<span class="absolute -bottom-1 -right-1 text-[10px] font-medium rounded-full w-4 h-4 flex items-center justify-center text-white"
:class="[
index === 0 ? 'bg-yellow-400' :
index === 1 ? 'bg-gray-300' :
index === 2 ? 'bg-orange-400' :
'bg-gray-500'
]">{{ index + 1 }}</span>
</div>
</div>
</div>
</div>
<button @click.stop="showRankingList = true"
class="flex-none h-8 w-8 flex items-center justify-center text-white cursor-pointer">
<i class="fas fa-chevron-right"></i>
</button>
</div>
<button class="flex-none w-8 h-8 mt-2 mr-2 flex items-center justify-center text-white bg-black/40 rounded-full cursor-pointer" @click="handleExit">
<i class="fas fa-sign-out-alt"></i>
</button>
</div>
<!-- 攝影機選項彈窗 -->
<div v-if="showCameraOptions" class="absolute right-14 top-1/3 bg-black/70 rounded-lg p-2 text-white text-sm">
<div class="flex flex-col space-y-2">
<button class="px-3 py-1.5 rounded-lg cursor-pointer" :class="{'bg-pink-500': currentCamera === 'main'}">主鏡頭</button>
<button class="px-3 py-1.5 rounded-lg cursor-pointer" :class="{'bg-pink-500': currentCamera === 'audience'}">觀眾視角</button>
<button class="px-3 py-1.5 rounded-lg cursor-pointer" :class="{'bg-pink-500': currentCamera === 'closeup'}">特寫鏡頭</button>
</div>
</div>
<!-- 聊天室 -->
<div class="absolute bottom-16 left-0 right-0 max-h-[40%] overflow-y-auto px-2" ref="chatContainer">
<div v-for="(message, index) in chatMessages" :key="index" class="mb-2">
<div class="inline-block bg-black/30 rounded-lg px-2 py-1 max-w-[85%]">
<div class="flex items-center">
<img :src="message.avatar" alt="用戶頭像" class="w-6 h-6 rounded-full cursor-pointer" @click.stop="showUserInfo(message.userId)" />
<span class="font-medium ml-1 cursor-pointer" :class="message.isVIP ? 'text-yellow-400' : 'text-white'" @click.stop="showUserInfo(message.userId)">{{ message.username }}</span>
<span v-if="message.isVIP" class="ml-1 text-xs bg-gradient-to-r from-yellow-400 to-yellow-600 px-1 rounded text-white">VIP</span>
</div>
<p class="text-white text-sm mt-0.5">{{ message.content }}</p>
</div>
</div>
</div>
<!-- 禮物顯示區域 -->
<div class="absolute top-1/4 left-0 right-0 pointer-events-none">
<transition-group name="gift">
<div v-for="gift in activeGifts" :key="gift.id" class="gift-animation bg-gradient-to-r from-purple-600/80 to-pink-600/80 rounded-lg px-3 py-2 mb-2 mx-auto max-w-[80%] flex items-center">
<img :src="gift.senderAvatar" alt="贈送者頭像" class="w-8 h-8 rounded-full" />
<div class="ml-2 text-white">
<div class="text-sm font-medium">{{ gift.senderName }} 贈送</div>
<div class="flex items-center">
<img :src="gift.giftImage" alt="禮物" class="w-6 h-6" />
<span class="ml-1 text-yellow-300 text-xs">{{ gift.giftName }}</span>
<span class="ml-1 text-white text-xs">x{{ gift.count }}</span>
</div>
</div>
</div>
</transition-group>
</div>
<!-- 底部控制欄 -->
<div class="absolute bottom-0 left-0 right-0 px-4 py-3 bg-gradient-to-t from-black/70 to-transparent">
<div class="flex items-center space-x-2">
<div class="flex-1 bg-white/20 rounded-full px-4 py-2 flex items-center text-white">
<i class="fas fa-comment-alt mr-2"></i>
<input
type="text"
v-model="newMessage"
placeholder="說點什麼..."
class="bg-transparent border-none outline-none flex-1 text-sm"
@keyup.enter="sendMessage"
/>
<i class="fas fa-smile ml-2"></i>
</div>
<button @click="sendMessage" class="bg-white/20 h-10 w-10 rounded-full flex items-center justify-center text-white cursor-pointer">
<i class="fas fa-paper-plane"></i>
</button>
<button @click.stop="showGiftPanel = true" class="bg-white/20 h-10 w-10 rounded-full flex items-center justify-center text-white cursor-pointer">
<i class="fas fa-gift"></i>
</button>
</div>
</div>
</div>
<!-- 主播信息卡片 -->
<div v-if="showHostInfo" class="fixed inset-0 bg-black/60 z-10 flex items-center justify-center" @click.self="showHostInfo = false">
<div class="bg-gray-800 rounded-xl w-[90%] max-w-sm overflow-hidden text-white">
<div class="relative">
<img :src="hostInfo.coverImage" alt="主播封面" class="w-full h-40 object-cover" />
<div class="absolute top-2 right-2">
<button @click="showHostInfo = false" class="bg-black/50 w-8 h-8 rounded-full flex items-center justify-center text-white cursor-pointer">
<i class="fas fa-times"></i>
</button>
</div>
<div class="absolute -bottom-12 left-4">
<img :src="hostInfo.avatar" alt="主播頭像" class="w-24 h-24 rounded-full border-4 border-gray-800" />
</div>
</div>
<div class="pt-14 px-4 pb-4">
<div class="flex justify-between items-start">
<div>
<h3 class="text-lg font-bold">{{ hostInfo.name }}</h3>
<p class="text-gray-400 text-sm">ID: {{ hostInfo.id }}</p>
</div>
<button class="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-4 py-1.5 rounded-full text-sm !rounded-button cursor-pointer">
{{ isFollowing ? '已追蹤' : '+ 追蹤' }}
</button>
</div>
<div class="flex mt-4 text-center">
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(hostInfo.followers) }}</div>
<div class="text-gray-400 text-xs">粉絲</div>
</div>
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(hostInfo.following) }}</div>
<div class="text-gray-400 text-xs">追蹤</div>
</div>
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(hostInfo.likes) }}</div>
<div class="text-gray-400 text-xs">獲讚</div>
</div>
</div>
<div class="mt-4 text-sm text-gray-300">
<p>{{ hostInfo.bio }}</p>
</div>
<div class="mt-4 pt-4 border-t border-gray-700">
<h4 class="text-sm font-medium">本場直播收益</h4>
<div class="flex items-center mt-2">
<img :src="diamondIcon" alt="鑽石" class="w-5 h-5" />
<span class="ml-1 text-lg font-bold text-pink-500">{{ formatNumber(totalDiamonds) }}</span>
<span class="ml-1 text-xs text-gray-400">鑽石</span>
</div>
</div>
<div class="mt-4 flex space-x-2">
<button class="flex-1 bg-gray-700 text-white py-2 rounded-lg text-sm flex items-center justify-center !rounded-button cursor-pointer">
<i class="fas fa-envelope mr-1"></i> 私訊
</button>
<button class="flex-1 bg-gray-700 text-white py-2 rounded-lg text-sm flex items-center justify-center !rounded-button cursor-pointer">
<i class="fas fa-user mr-1"></i> 查看主頁
</button>
</div>
</div>
</div>
</div>
<!-- 用戶信息卡片 -->
<div v-if="showUserInfoCard" class="fixed inset-0 bg-black/60 z-50 flex items-center justify-center" @click.self="showUserInfoCard = false">
<div class="bg-gray-800 rounded-xl w-[90%] max-w-sm overflow-hidden text-white">
<div class="relative">
<img :src="selectedUser.coverImage" alt="用戶封面" class="w-full h-32 object-cover" />
<div class="absolute top-2 right-2">
<button @click="showUserInfoCard = false" class="bg-black/50 w-8 h-8 rounded-full flex items-center justify-center text-white cursor-pointer">
<i class="fas fa-times"></i>
</button>
</div>
<div class="absolute -bottom-10 left-4">
<img :src="selectedUser.avatar" alt="用戶頭像" class="w-20 h-20 rounded-full border-4 border-gray-800" />
</div>
</div>
<div class="pt-12 px-4 pb-4">
<div class="flex justify-between items-start">
<div>
<h3 class="text-lg font-bold">{{ selectedUser.name }}</h3>
<p class="text-gray-400 text-sm">ID: {{ selectedUser.id }}</p>
</div>
<button class="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-4 py-1.5 rounded-full text-sm !rounded-button cursor-pointer">
{{ selectedUser.isFollowing ? '已追蹤' : '+ 追蹤' }}
</button>
</div>
<div class="flex mt-4 text-center">
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(selectedUser.followers) }}</div>
<div class="text-gray-400 text-xs">粉絲</div>
</div>
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(selectedUser.following) }}</div>
<div class="text-gray-400 text-xs">追蹤</div>
</div>
<div class="flex-1">
<div class="text-lg font-medium">{{ formatNumber(selectedUser.likes) }}</div>
<div class="text-gray-400 text-xs">獲讚</div>
</div>
</div>
<div class="mt-4 text-sm text-gray-300">
<p>{{ selectedUser.bio }}</p>
</div>
<div class="mt-4 flex space-x-2">
<button class="flex-1 bg-gray-700 text-white py-2 rounded-lg text-sm flex items-center justify-center !rounded-button cursor-pointer">
<i class="fas fa-envelope mr-1"></i> 私訊
</button>
<button class="flex-1 bg-gray-700 text-white py-2 rounded-lg text-sm flex items-center justify-center !rounded-button cursor-pointer">
<i class="fas fa-user mr-1"></i> 查看主頁
</button>
</div>
</div>
</div>
</div>
<!-- 禮物面板 -->
<div v-if="showGiftPanel" class="fixed inset-x-0 bottom-0 bg-gray-800 rounded-t-xl z-10 text-white">
<div class="p-4 max-h-[60vh] flex flex-col">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-bold">禮物</h3>
<button @click="showGiftPanel = false" class="text-gray-400 cursor-pointer">
<i class="fas fa-times"></i>
</button>
</div>
<div class="flex space-x-4 mb-4 overflow-x-auto hide-scrollbar">
<button v-for="category in giftCategories" :key="category.id"
@click="currentGiftCategory = category.id"
class="px-3 py-1 rounded-full text-sm cursor-pointer flex-shrink-0"
:class="currentGiftCategory === category.id ? 'bg-pink-500 text-white' : 'bg-gray-700 text-gray-300'">
{{ category.name }}
</button>
</div>
<div class="grid grid-cols-4 gap-4 overflow-y-auto gift-grid flex-1">
<div v-for="gift in filteredGifts" :key="gift.id"
@click="selectGift(gift)"
class="flex flex-col items-center cursor-pointer"
:class="{'border-2 border-pink-500 rounded-lg': selectedGift?.id === gift.id}">
<div class="w-14 h-14 rounded-lg bg-gray-700 flex items-center justify-center mb-1">
<img :src="gift.image" alt="禮物" class="w-10 h-10" />
</div>
<span class="text-xs">{{ gift.name }}</span>
<div class="flex items-center text-xs text-pink-500">
<img :src="diamondIcon" alt="鑽石" class="w-3 h-3 mr-0.5" />
<span>{{ gift.price }}</span>
</div>
</div>
</div>
<div class="mt-4 flex items-center justify-between border-t border-gray-700 pt-4">
<div class="flex items-center">
<img :src="diamondIcon" alt="鑽石" class="w-5 h-5" />
<span class="ml-1 font-medium">{{ userDiamonds }}</span>
<button class="ml-2 text-xs text-blue-400 cursor-pointer">儲值</button>
</div>
<div class="flex items-center space-x-3">
<div class="relative">
<select v-model="giftCount" class="appearance-none bg-gray-700 rounded-lg px-3 py-2 pr-8 text-sm text-white border-none">
<option v-for="count in giftCounts" :key="count" :value="count">x{{ count }}</option>
</select>
<div class="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400">
<i class="fas fa-chevron-down text-xs"></i>
</div>
</div>
<button @click="sendSelectedGift"
class="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-6 py-2 rounded-full !rounded-button cursor-pointer"
:disabled="!selectedGift">
送出
</button>
</div>
</div>
</div>
</div>
<!-- 禮物排行榜 -->
<div v-if="showRankingList" class="fixed inset-y-0 right-0 w-3/4 bg-gray-800 z-20 overflow-hidden text-white">
<div class="p-4">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-bold">禮物排行榜</h3>
<button @click="showRankingList = false" class="text-gray-400 cursor-pointer">
<i class="fas fa-times"></i>
</button>
</div>
<div class="flex border-b border-gray-700">
<button
v-for="tab in rankingTabs"
:key="tab.id"
@click="currentRankingTab = tab.id"
class="flex-1 py-2 text-center text-sm cursor-pointer"
:class="{'text-pink-500 border-b-2 border-pink-500 font-medium': currentRankingTab === tab.id, 'text-gray-400': currentRankingTab !== tab.id}"
>
{{ tab.name }}
</button>
</div>
<!-- TOP 3 用戶 -->
<div class="flex justify-around py-4">
<!-- 第二名 -->
<div class="flex flex-col items-center" v-if="rankingList.length > 1">
<div class="relative cursor-pointer" @click.stop="showUserInfo(rankingList[1].id)">
<img :src="rankingList[1].avatar" alt="用戶頭像" class="w-16 h-16 rounded-full border-2 border-blue-400" />
<div class="absolute -bottom-2 -right-2 w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center text-white text-xs font-bold">
2
</div>
</div>
<div class="mt-2 text-center">
<div class="text-xs font-medium truncate w-16">{{ rankingList[1].name }}</div>
<div class="flex items-center justify-center text-xs text-pink-500 mt-1">
<img :src="diamondIcon" alt="鑽石" class="w-3 h-3 mr-0.5" />
<span>{{ formatNumber(rankingList[1].diamonds) }}</span>
</div>
</div>
</div>
<!-- 第一名 -->
<div class="flex flex-col items-center -mt-4" v-if="rankingList.length > 0">
<div class="relative cursor-pointer" @click.stop="showUserInfo(rankingList[0].id)">
<img :src="rankingList[0].avatar" alt="用戶頭像" class="w-20 h-20 rounded-full border-2 border-yellow-400" />
<div class="absolute -bottom-2 -right-2 w-7 h-7 bg-yellow-400 rounded-full flex items-center justify-center text-white text-xs font-bold">
1
</div>
</div>
<div class="mt-2 text-center">
<div class="text-xs font-medium truncate w-20">{{ rankingList[0].name }}</div>
<div class="flex items-center justify-center text-xs text-pink-500 mt-1">
<img :src="diamondIcon" alt="鑽石" class="w-3 h-3 mr-0.5" />
<span>{{ formatNumber(rankingList[0].diamonds) }}</span>
</div>
</div>
</div>
<!-- 第三名 -->
<div class="flex flex-col items-center" v-if="rankingList.length > 2">
<div class="relative cursor-pointer" @click.stop="showUserInfo(rankingList[2].id)">
<img :src="rankingList[2].avatar" alt="用戶頭像" class="w-16 h-16 rounded-full border-2 border-orange-400" />
<div class="absolute -bottom-2 -right-2 w-6 h-6 bg-orange-400 rounded-full flex items-center justify-center text-white text-xs font-bold">
3
</div>
</div>
<div class="mt-2 text-center">
<div class="text-xs font-medium truncate w-16">{{ rankingList[2].name }}</div>
<div class="flex items-center justify-center text-xs text-pink-500 mt-1">
<img :src="diamondIcon" alt="鑽石" class="w-3 h-3 mr-0.5" />
<span>{{ formatNumber(rankingList[2].diamonds) }}</span>
</div>
</div>
</div>
</div>
<!-- 其他排名 -->
<div class="mt-2">
<div v-for="(user, index) in rankingList.slice(3)" :key="user.id" class="flex items-center py-2 border-b border-gray-700">
<div class="w-6 text-center text-gray-400 font-medium">{{ index + 4 }}</div>
<img :src="user.avatar" alt="用戶頭像" class="w-10 h-10 rounded-full ml-2 cursor-pointer" @click.stop="showUserInfo(user.id)" />
<div class="ml-2 flex-1">
<div class="text-sm font-medium">{{ user.name }}</div>
<div class="text-xs text-gray-400">貢獻值 {{ formatNumber(user.contribution) }}</div>
</div>
<div class="flex items-center text-pink-500">
<img :src="diamondIcon" alt="鑽石" class="w-4 h-4 mr-0.5" />
<span>{{ formatNumber(user.diamonds) }}</span>
</div>
</div>
</div>
</div>
</div>
<!-- 系統通知 -->
<transition name="notification">
<div v-if="notification.show" class="fixed top-16 left-0 right-0 flex justify-center">
<div class="bg-black/70 text-white px-4 py-2 rounded-full text-sm max-w-[80%] text-center">
{{ notification.message }}
</div>
</div>
</transition>
</div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed, nextTick } from 'vue';
// 主播信息
const hostInfo = ref({
id: 'host88888',
name: '陳美美',
avatar: 'https://readdy.ai/api/search-image?query=beautiful%20Asian%20female%20streamer%20with%20natural%20makeup%2C%20professional%20portrait%2C%20soft%20lighting%2C%20clear%20facial%20features%2C%20friendly%20smile%2C%20high%20quality%20studio%20photo%2C%20isolated%20on%20soft%20gradient%20background&width=100&height=100&seq=1&orientation=squarish',
coverImage: 'https://readdy.ai/api/search-image?query=colorful%20abstract%20streaming%20background%20with%20music%20notes%20and%20vibrant%20colors%2C%20digital%20art%20style%2C%20perfect%20for%20live%20streaming%20cover%2C%20professional%20design%20with%20soft%20gradient%20and%20dynamic%20elements&width=400&height=200&seq=2&orientation=landscape',
followers: 1250000,
following: 85,
likes: 25000000,
bio: '嗨！我是美美，專業歌手兼直播主，喜歡唱歌、旅行和美食。每週二、四、六晚上8點準時直播，歡迎來聊天互動！'
});
// 在線人數
const onlineCount = ref(12580);
// 圖片資源
const liveStreamUrl = ref('https://readdy.ai/api/search-image?query=professional%20female%20singer%20performing%20on%20stage%20with%20microphone%2C%20concert%20lighting%2C%20enthusiastic%20audience%20visible%20in%20background%2C%20high%20energy%20performance%2C%20vibrant%20atmosphere%2C%20high%20quality%20photography&width=375&height=762&seq=3&orientation=portrait');
const diamondIcon = ref('https://readdy.ai/api/search-image?query=shiny%20blue%20diamond%20icon%2C%203D%20rendering%2C%20glossy%20surface%20with%20reflections%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20mobile%20app%20UI%2C%20game%20asset%20style&width=50&height=50&seq=4&orientation=squarish');
// 畫質設置
const currentQuality = ref('高清');
const qualityOptions = ['自動', '高清', '流暢'];
// 攝影機選項
const showCameraOptions = ref(false);
const currentCamera = ref('main');
// 主播信息卡片
const showHostInfo = ref(false);
const isFollowing = ref(true);
// 用戶信息卡片
const showUserInfoCard = ref(false);
const selectedUser = ref({
id: '',
name: '',
avatar: '',
coverImage: '',
followers: 0,
following: 0,
likes: 0,
bio: '',
isFollowing: false
});
// 聊天相關
const chatMessages = ref([
{ userId: 'user1', username: '王小明', content: '主播今天好漂亮！', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20headshot&width=100&height=100&seq=28&orientation=squarish' },
{ userId: 'user2', username: '林大熊', content: '歌聲太好聽了，支持支持！', isVIP: true, avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20businessman%20with%20glasses%2C%20professional%20headshot&width=100&height=100&seq=29&orientation=squarish' },
{ userId: 'user3', username: '陳小花', content: '請問這首歌叫什麼名字？', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20cute%20hairstyle%2C%20professional%20headshot&width=100&height=100&seq=30&orientation=squarish' },
{ userId: 'user4', username: '張美麗', content: '主播可以唱一首周杰倫的歌嗎？', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20elegant%20style%2C%20professional%20headshot&width=100&height=100&seq=31&orientation=squarish' },
{ userId: 'user5', username: '黃金VIP', content: '已經追蹤你三年了，每次直播都不會錯過！', isVIP: true, avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20male%20with%20confident%20expression%2C%20professional%20headshot&width=100&height=100&seq=32&orientation=squarish' },
{ userId: 'user6', username: '李小龍', content: '主播好棒，送你一個大禮物！', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20sporty%20style%2C%20professional%20headshot&width=100&height=100&seq=33&orientation=squarish' },
{ userId: 'user7', username: '吳大哥', content: '這個編曲真的很特別，很有感覺', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20male%20with%20business%20casual%20style%2C%20professional%20headshot&width=100&height=100&seq=34&orientation=squarish' },
{ userId: 'user8', username: '趙天王', content: '主播今天狀態超好的！', isVIP: true, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20trendy%20style%2C%20professional%20headshot&width=100&height=100&seq=35&orientation=squarish' }
]);
const newMessage = ref('');
const chatContainer = ref<HTMLElement | null>(null);
// 點讚相關
const isLiked = ref(false);
const likeCount = ref(8754);
// 禮物相關
const showGiftPanel = ref(false);
const userDiamonds = ref(9999);
const totalDiamonds = ref(156789);
const selectedGift = ref(null);
const giftCount = ref(1);
const giftCategories = ref([
{ id: 'popular', name: '熱門' },
{ id: 'basic', name: '基礎' },
{ id: 'special', name: '特別' },
{ id: 'luxury', name: '豪華' }
]);
const currentGiftCategory = ref('popular');
// 擴展禮物列表到30個
const gifts = ref([
{ id: 1, name: '愛心', price: 5, image: 'https://readdy.ai/api/search-image?query=cute%20pink%20heart%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=5&orientation=squarish', category: 'basic' },
{ id: 2, name: '玫瑰花', price: 20, image: 'https://readdy.ai/api/search-image?query=beautiful%20red%20rose%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=6&orientation=squarish', category: 'basic' },
{ id: 3, name: '蛋糕', price: 50, image: 'https://readdy.ai/api/search-image?query=delicious%20birthday%20cake%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=7&orientation=squarish', category: 'popular' },
{ id: 4, name: '皇冠', price: 100, image: 'https://readdy.ai/api/search-image?query=golden%20crown%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=8&orientation=squarish', category: 'special' },
{ id: 5, name: '跑車', price: 500, image: 'https://readdy.ai/api/search-image?query=luxury%20sports%20car%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=9&orientation=squarish', category: 'luxury' },
{ id: 6, name: '火箭', price: 1000, image: 'https://readdy.ai/api/search-image?query=colorful%20rocket%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=10&orientation=squarish', category: 'luxury' },
{ id: 7, name: '城堡', price: 2000, image: 'https://readdy.ai/api/search-image?query=fantasy%20castle%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=11&orientation=squarish', category: 'luxury' },
{ id: 8, name: '遊艇', price: 5000, image: 'https://readdy.ai/api/search-image?query=luxury%20yacht%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=12&orientation=squarish', category: 'luxury' },
// 添加更多禮物到各個分類
{ id: 9, name: '氣球', price: 10, image: 'https://readdy.ai/api/search-image?query=colorful%20balloon%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=42&orientation=squarish', category: 'basic' },
{ id: 10, name: '棒棒糖', price: 15, image: 'https://readdy.ai/api/search-image?query=colorful%20lollipop%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=43&orientation=squarish', category: 'basic' },
{ id: 11, name: '冰淇淋', price: 25, image: 'https://readdy.ai/api/search-image?query=ice%20cream%20cone%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=44&orientation=squarish', category: 'basic' },
{ id: 12, name: '星星', price: 30, image: 'https://readdy.ai/api/search-image?query=golden%20star%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=45&orientation=squarish', category: 'basic' },
{ id: 13, name: '麥克風', price: 60, image: 'https://readdy.ai/api/search-image?query=microphone%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=46&orientation=squarish', category: 'popular' },
{ id: 14, name: '鑽石', price: 80, image: 'https://readdy.ai/api/search-image?query=diamond%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=47&orientation=squarish', category: 'popular' },
{ id: 15, name: '熱氣球', price: 120, image: 'https://readdy.ai/api/search-image?query=hot%20air%20balloon%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=48&orientation=squarish', category: 'popular' },
{ id: 16, name: '煙花', price: 150, image: 'https://readdy.ai/api/search-image?query=fireworks%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=49&orientation=squarish', category: 'popular' },
{ id: 17, name: '小熊', price: 200, image: 'https://readdy.ai/api/search-image?query=teddy%20bear%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=50&orientation=squarish', category: 'popular' },
{ id: 18, name: '寶箱', price: 250, image: 'https://readdy.ai/api/search-image?query=treasure%20chest%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=51&orientation=squarish', category: 'special' },
{ id: 19, name: '飛機', price: 300, image: 'https://readdy.ai/api/search-image?query=airplane%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=52&orientation=squarish', category: 'special' },
{ id: 20, name: '直升機', price: 350, image: 'https://readdy.ai/api/search-image?query=helicopter%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=53&orientation=squarish', category: 'special' },
{ id: 21, name: '遊輪', price: 400, image: 'https://readdy.ai/api/search-image?query=cruise%20ship%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=54&orientation=squarish', category: 'special' },
{ id: 22, name: '別墅', price: 600, image: 'https://readdy.ai/api/search-image?query=luxury%20villa%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=55&orientation=squarish', category: 'luxury' },
{ id: 23, name: '私人飛機', price: 800, image: 'https://readdy.ai/api/search-image?query=private%20jet%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=56&orientation=squarish', category: 'luxury' },
{ id: 24, name: '超級遊艇', price: 1500, image: 'https://readdy.ai/api/search-image?query=super%20yacht%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=57&orientation=squarish', category: 'luxury' },
{ id: 25, name: '島嶼', price: 3000, image: 'https://readdy.ai/api/search-image?query=private%20island%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=58&orientation=squarish', category: 'luxury' },
{ id: 26, name: '太空船', price: 8000, image: 'https://readdy.ai/api/search-image?query=spaceship%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=59&orientation=squarish', category: 'luxury' },
{ id: 27, name: '月球基地', price: 10000, image: 'https://readdy.ai/api/search-image?query=moon%20base%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=60&orientation=squarish', category: 'luxury' },
{ id: 28, name: '星球', price: 15000, image: 'https://readdy.ai/api/search-image?query=planet%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=61&orientation=squarish', category: 'luxury' },
{ id: 29, name: '銀河系', price: 20000, image: 'https://readdy.ai/api/search-image?query=galaxy%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=62&orientation=squarish', category: 'luxury' },
{ id: 30, name: '宇宙', price: 50000, image: 'https://readdy.ai/api/search-image?query=universe%20gift%20icon%2C%203D%20cartoon%20style%2C%20glossy%20surface%2C%20isolated%20on%20transparent%20background%2C%20perfect%20for%20streaming%20app&width=80&height=80&seq=63&orientation=squarish', category: 'luxury' }
]);
const giftCounts = ref([1, 6, 66, 168, 888, 1314, 8888]);
const filteredGifts = computed(() => {
return gifts.value.filter(gift => gift.category === currentGiftCategory.value);
});
const activeGifts = ref<Array<{id: number, senderName: string, senderAvatar: string, giftName: string, giftImage: string, count: number}>>([]);
let giftIdCounter = 0;
// 禮物排行榜
const showRankingList = ref(false);
const currentRankingTab = ref('current');
const rankingTabs = [
{ id: 'current', name: '本場榜' },
{ id: 'weekly', name: '週榜' },
{ id: 'monthly', name: '月榜' }
];
const rankingList = ref([
{ id: 'user1', name: '超級土豪', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20stylish%20haircut%20and%20confident%20expression%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=13&orientation=squarish', diamonds: 50000, contribution: 98765 },
{ id: 'user2', name: '富二代', avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20businessman%20with%20glasses%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=14&orientation=squarish', diamonds: 30000, contribution: 65432 },
{ id: 'user3', name: '小公主', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20cute%20hairstyle%20and%20playful%20expression%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=15&orientation=squarish', diamonds: 20000, contribution: 43210 },
{ id: 'user4', name: '快樂每一天', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=16&orientation=squarish', diamonds: 15000, contribution: 32109 },
{ id: 'user5', name: '開心果', avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20female%20with%20elegant%20style%20and%20warm%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=17&orientation=squarish', diamonds: 12000, contribution: 28765 },
{ id: 'user6', name: '夢想家', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20artistic%20style%20and%20thoughtful%20expression%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=18&orientation=squarish', diamonds: 10000, contribution: 21543 },
{ id: 'user7', name: '小確幸', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20natural%20style%20and%20genuine%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=19&orientation=squarish', diamonds: 8000, contribution: 19876 }
]);
// 系統通知
const notification = ref({
show: false,
message: ''
});
// 方法
const formatNumber = (num: number): string => {
if (num >= 10000) {
return (num / 10000).toFixed(1) + '萬';
}
return num.toString();
};
const handleLike = () => {
isLiked.value = true;
likeCount.value += 1;
// 模擬點讚動畫效果
setTimeout(() => {
showNotification('感謝您的點讚！');
}, 300);
};
const sendMessage = () => {
if (!newMessage.value.trim()) return;
chatMessages.value.push({
userId: 'currentUser',
username: '我',
content: newMessage.value,
isVIP: false,
avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20headshot&width=100&height=100&seq=40&orientation=squarish'
});
newMessage.value = '';
// 滾動到最新消息
nextTick(() => {
if (chatContainer.value) {
chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
}
});
};
const showUserInfo = (userId: string) => {
// 模擬獲取用戶信息
const userProfiles = {
'user1': {
id: 'user1',
name: '王小明',
avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20stylish%20haircut%20and%20confident%20expression%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=20&orientation=squarish',
coverImage: 'https://readdy.ai/api/search-image?query=urban%20cityscape%20at%20sunset%20with%20modern%20buildings%2C%20perfect%20for%20profile%20cover%2C%20vibrant%20colors%2C%20panoramic%20view&width=400&height=200&seq=21&orientation=landscape',
followers: 1250,
following: 385,
likes: 8520,
bio: '喜歡音樂、旅行和美食的普通上班族，閒暇時間喜歡看直播放鬆心情。',
isFollowing: true
},
'user2': {
id: 'user2',
name: '林大熊',
avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20businessman%20with%20glasses%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=22&orientation=squarish',
coverImage: 'https://readdy.ai/api/search-image?query=mountain%20landscape%20with%20lake%20reflection%2C%20perfect%20for%20profile%20cover%2C%20serene%20nature%20scene%2C%20panoramic%20view&width=400&height=200&seq=23&orientation=landscape',
followers: 5680,
following: 120,
likes: 15900,
bio: 'VIP會員，音樂製作人，喜歡發掘有才華的歌手。',
isFollowing: false
},
'user3': {
id: 'user3',
name: '陳小花',
avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20cute%20hairstyle%2C%20professional%20portrait&width=100&height=100&seq=30&orientation=squarish',
coverImage: 'https://readdy.ai/api/search-image?query=cherry%20blossom%20garden%2C%20perfect%20for%20profile%20cover&width=400&height=200&seq=40&orientation=landscape',
followers: 2360,
following: 450,
likes: 12800,
bio: '熱愛音樂的小女生，喜歡唱歌和跳舞。希望能在這裡認識更多志同道合的朋友！',
isFollowing: false
}
};
// 如果找不到用戶資料，生成一個默認的資料
const user = userProfiles[userId] || {
id: userId,
name: chatMessages.value.find(msg => msg.userId === userId)?.username || '用戶',
avatar: chatMessages.value.find(msg => msg.userId === userId)?.avatar || 'default-avatar.png',
coverImage: 'https://readdy.ai/api/search-image?query=abstract%20colorful%20background%2C%20perfect%20for%20profile%20cover&width=400&height=200&seq=41&orientation=landscape',
followers: Math.floor(Math.random() * 5000) + 100,
following: Math.floor(Math.random() * 500) + 50,
likes: Math.floor(Math.random() * 50000) + 1000,
bio: '這位用戶很神秘，還沒有留下個人簡介。',
isFollowing: false
};
selectedUser.value = user;
showUserInfoCard.value = true;
};
const selectGift = (gift: {id: number, name: string, price: number, image: string}) => {
selectedGift.value = gift;
};
const increaseGiftCount = () => {
if (giftCount.value < 99) {
giftCount.value++;
}
};
const decreaseGiftCount = () => {
if (giftCount.value > 1) {
giftCount.value--;
}
};
const sendSelectedGift = () => {
if (!selectedGift.value) return;
const totalPrice = selectedGift.value.price * giftCount.value;
if (userDiamonds.value >= totalPrice) {
userDiamonds.value -= totalPrice;
totalDiamonds.value += totalPrice;
// 顯示禮物動畫
const newGift = {
id: giftIdCounter++,
senderName: '我',
senderAvatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=24&orientation=squarish',
giftName: selectedGift.value.name,
giftImage: selectedGift.value.image,
count: giftCount.value
};
activeGifts.value.push(newGift);
// 3秒後移除禮物動畫
setTimeout(() => {
activeGifts.value = activeGifts.value.filter(g => g.id !== newGift.id);
}, 3000);
showNotification(`成功送出 ${giftCount.value} 個 ${selectedGift.value.name}！`);
showGiftPanel.value = false;
selectedGift.value = null;
giftCount.value = 1;
} else {
showNotification('鑽石不足，請先儲值');
}
};
const sendGift = (gift: {id: number, name: string, price: number, image: string}) => {
// 模擬發送禮物
if (userDiamonds.value >= gift.price) {
userDiamonds.value -= gift.price;
totalDiamonds.value += gift.price;
// 顯示禮物動畫
const newGift = {
id: giftIdCounter++,
senderName: '我',
senderAvatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=24&orientation=squarish',
giftName: gift.name,
giftImage: gift.image,
count: 1
};
activeGifts.value.push(newGift);
// 3秒後移除禮物動畫
setTimeout(() => {
activeGifts.value = activeGifts.value.filter(g => g.id !== newGift.id);
}, 3000);
showNotification(`成功送出 ${gift.name}！`);
showGiftPanel.value = false;
} else {
showNotification('鑽石不足，請先儲值');
}
};
const showNotification = (message: string) => {
notification.value = {
show: true,
message
};
setTimeout(() => {
notification.value.show = false;
}, 3000);
};
// 點擊背景區域關閉彈窗
const handleBackgroundClick = () => {
if (showRankingList.value) {
showRankingList.value = false;
}
if (showCameraOptions.value) {
showCameraOptions.value = false;
}
if (showGiftPanel.value) {
showGiftPanel.value = false;
}
};
// Add exit handler
const handleExit = () => {
// Here you can add navigation logic to return to the live stream list page
showNotification('正在返回直播列表...');
};
onMounted(() => {
// 模擬接收新消息
setInterval(() => {
const randomMessages = [
{ userId: 'user9', username: '快樂每一天', content: '主播唱歌真好聽！', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20bright%20smile%2C%20professional%20headshot&width=100&height=100&seq=36&orientation=squarish' },
{ userId: 'user10', username: '音樂發燒友', content: '這首歌的編曲太棒了', isVIP: true, avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20male%20with%20artistic%20style%2C%20professional%20headshot&width=100&height=100&seq=37&orientation=squarish' },
{ userId: 'user11', username: '小確幸', content: '主播今天造型很美', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20natural%20makeup%2C%20professional%20headshot&width=100&height=100&seq=38&orientation=squarish' },
{ userId: 'user12', username: '夢想家', content: '請問這首歌的名字是？', isVIP: false, avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20creative%20style%2C%20professional%20headshot&width=100&height=100&seq=39&orientation=squarish' }
];
const randomIndex = Math.floor(Math.random() * randomMessages.length);
chatMessages.value.push(randomMessages[randomIndex]);
if (chatMessages.value.length > 50) {
chatMessages.value.shift();
}
// 滾動到最新消息
nextTick(() => {
if (chatContainer.value) {
chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
}
});
}, 5000);
// 模擬收到禮物
setInterval(() => {
if (Math.random() > 0.7) {
const randomGiftIndex = Math.floor(Math.random() * gifts.value.length);
const randomGift = gifts.value[randomGiftIndex];
const randomUsers = [
{ name: '快樂每一天', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20male%20with%20casual%20style%20and%20friendly%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=25&orientation=squarish' },
{ name: '小確幸', avatar: 'https://readdy.ai/api/search-image?query=young%20Asian%20female%20with%20natural%20style%20and%20genuine%20smile%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=26&orientation=squarish' },
{ name: '音樂發燒友', avatar: 'https://readdy.ai/api/search-image?query=middle%20aged%20Asian%20male%20with%20glasses%20and%20serious%20expression%2C%20professional%20portrait%2C%20high%20quality%20studio%20photo%20with%20soft%20lighting&width=100&height=100&seq=27&orientation=squarish' }
];
const randomUserIndex = Math.floor(Math.random() * randomUsers.length);
const randomUser = randomUsers[randomUserIndex];
const newGift = {
id: giftIdCounter++,
senderName: randomUser.name,
senderAvatar: randomUser.avatar,
giftName: randomGift.name,
giftImage: randomGift.image,
count: Math.floor(Math.random() * 5) + 1
};
activeGifts.value.push(newGift);
totalDiamonds.value += randomGift.price * newGift.count;
// 3秒後移除禮物動畫
setTimeout(() => {
activeGifts.value = activeGifts.value.filter(g => g.id !== newGift.id);
}, 3000);
}
}, 8000);
// 模擬系統通知
setTimeout(() => {
showNotification('歡迎來到陳美美的直播間！');
}, 2000);
// 初始滾動到最新消息
nextTick(() => {
if (chatContainer.value) {
chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
}
});
});
</script>
<style scoped>
.hide-scrollbar::-webkit-scrollbar {
display: none;
}
.hide-scrollbar {
-ms-overflow-style: none;
scrollbar-width: none;
}
.gift-animation {
animation: slideIn 0.5s ease-out;
}
@keyframes slideIn {
from {
transform: translateX(-100%);
opacity: 0;
}
to {
transform: translateX(0);
opacity: 1;
}
}
.gift-enter-active {
animation: slideIn 0.5s ease-out;
}
.gift-leave-active {
animation: slideOut 0.5s ease-in;
}
@keyframes slideOut {
from {
transform: translateX(0);
opacity: 1;
}
to {
transform: translateX(100%);
opacity: 0;
}
}
.notification-enter-active,
.notification-leave-active {
transition: all 0.3s ease;
}
.notification-enter-from,
.notification-leave-to {
transform: translateY(-20px);
opacity: 0;
}
/* Dark mode specific styles */
input::placeholder {
color: rgba(255, 255, 255, 0.5);
}
select {
background-image: none;
}
/* 禮物網格樣式 */
.gift-grid {
padding-right: 4px;
scrollbar-width: thin;
scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}
.gift-grid::-webkit-scrollbar {
width: 4px;
}
.gift-grid::-webkit-scrollbar-track {
background: rgba(255, 255, 255, 0.1);
border-radius: 4px;
}
.gift-grid::-webkit-scrollbar-thumb {
background: rgba(255, 255, 255, 0.3);
border-radius: 4px;
}
.gift-grid::-webkit-scrollbar-thumb:hover {
background: rgba(255, 255, 255, 0.5);
}
</style>
