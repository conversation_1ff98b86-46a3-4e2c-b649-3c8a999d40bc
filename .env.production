# 生產環境配置文件
# 複製此文件到服務器並根據實際情況修改

# 應用配置
NODE_ENV=production
PORT=5003
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production

# MongoDB 配置（使用外部 MongoDB 服務器）
MONGO_URI=***************************************************************************

# Redis 配置（可選）
REDIS_URL=redis://redis:6379

# 安全配置
CORS_ORIGIN=http://localhost:6666,https://liveshow.sotime.app

# 日誌配置
LOG_LEVEL=info

# 文件上傳配置
MAX_FILE_SIZE=100MB
# 已改用 GCP Storage，不再需要本地 uploads 目錄

# GCP Storage 配置
GCP_PROJECT_ID=ccw-dev
GCP_BUCKET_NAME=liveshowsotime
GCP_KEY_FILE=config/ccw-dev-81e940b67f37.json

# 郵件配置（如果需要）
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# 第三方服務配置
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=your-bucket-name
