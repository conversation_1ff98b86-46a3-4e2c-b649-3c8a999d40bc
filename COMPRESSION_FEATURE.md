# 檔案壓縮功能說明

## 功能概述

我已經為您的發文功能添加了自動檔案壓縮功能，可以在用戶選擇圖片和影片後自動進行壓縮，減少上傳檔案的大小，提升用戶體驗。

## 主要特性

### 🖼️ 圖片壓縮
- **支援格式**: JPEG, PNG, WebP
- **壓縮方式**: 使用 Canvas API 進行客戶端壓縮
- **預設設定**:
  - 最大尺寸: 1920x1080
  - 壓縮品質: 80%
  - 最大檔案大小: 1MB
  - 輸出格式: JPEG（更小的檔案大小）

### 🎬 影片壓縮
- **支援格式**: MP4, WebM, MOV 等
- **壓縮方式**: 使用 MediaRecorder API 進行客戶端壓縮
- **預設設定**:
  - 最大尺寸: 1280x720
  - 影片位元率: 1Mbps
  - 音訊位元率: 128kbps
  - 最大檔案大小: 10MB
  - 最大時長: 5分鐘

### 📊 壓縮統計
- 顯示壓縮比例
- 顯示原始檔案大小 vs 壓縮後大小
- 顯示總共節省的空間

## 使用者體驗

### 1. 檔案選擇
當用戶選擇圖片或影片時，系統會自動開始壓縮處理。

### 2. 壓縮進度
- 顯示壓縮進度條
- 顯示當前處理的檔案名稱
- 顯示處理進度百分比

### 3. 壓縮結果
- 在預覽圖片/影片上顯示壓縮比例標籤
- 在底部顯示總共節省的空間
- 對於單個檔案，顯示詳細的大小對比

## 技術實現

### 檔案結構
```
client/src/utils/
├── imageCompression.ts    # 圖片壓縮工具
├── videoCompression.ts    # 影片壓縮工具
└── fileCompression.ts     # 統一壓縮介面
```

### 核心功能

#### 圖片壓縮 (`imageCompression.ts`)
- `compressImage()`: 壓縮單張圖片
- `compressImages()`: 批量壓縮圖片
- `calculateNewDimensions()`: 計算新尺寸（保持比例）
- `formatFileSize()`: 格式化檔案大小顯示

#### 影片壓縮 (`videoCompression.ts`)
- `compressVideo()`: 壓縮單個影片
- `getVideoInfo()`: 獲取影片資訊
- `isVideoCompressionSupported()`: 檢查瀏覽器支援
- `formatDuration()`: 格式化時長顯示

#### 統一介面 (`fileCompression.ts`)
- `compressFile()`: 壓縮單個檔案（自動判斷類型）
- `compressFiles()`: 批量壓縮檔案
- `shouldCompress()`: 判斷是否需要壓縮
- `getCompressionStats()`: 獲取壓縮統計資訊

### UI 更新

#### Wall.vue 組件更新
1. **新增狀態變數**:
   - `isCompressing`: 壓縮進行中標誌
   - `compressionProgress`: 壓縮進度資訊
   - `compressionResults`: 壓縮結果

2. **UI 元素**:
   - 壓縮進度顯示區域
   - 壓縮比例標籤
   - 檔案大小對比資訊
   - 總節省空間統計

3. **Media 介面擴展**:
   ```typescript
   interface Media {
     // ... 原有屬性
     originalSize?: number;
     compressedSize?: number;
     compressionRatio?: number;
   }
   ```

## 壓縮設定

### 可調整參數

#### 圖片壓縮設定
```typescript
{
  maxWidth: 1920,        // 最大寬度
  maxHeight: 1080,       // 最大高度
  quality: 0.8,          // 壓縮品質 (0.1-1.0)
  format: 'jpeg',        // 輸出格式
  maxSizeKB: 1024        // 最大檔案大小 (KB)
}
```

#### 影片壓縮設定
```typescript
{
  maxWidth: 1280,        // 最大寬度
  maxHeight: 720,        // 最大高度
  quality: 0.7,          // 壓縮品質
  maxSizeKB: 10240,      // 最大檔案大小 (KB)
  maxDuration: 300,      // 最大時長 (秒)
  videoBitrate: 1000000, // 影片位元率 (bps)
  audioBitrate: 128000   // 音訊位元率 (bps)
}
```

## 瀏覽器相容性

### 圖片壓縮
- ✅ Chrome 51+
- ✅ Firefox 50+
- ✅ Safari 10+
- ✅ Edge 79+

### 影片壓縮
- ✅ Chrome 47+
- ✅ Firefox 25+
- ⚠️ Safari 14.1+ (部分支援)
- ✅ Edge 79+

**注意**: 如果瀏覽器不支援影片壓縮，系統會自動使用原始檔案。

## 效能優化

1. **客戶端處理**: 所有壓縮都在客戶端進行，不增加伺服器負擔
2. **批量處理**: 圖片支援批量壓縮，提升效率
3. **進度回饋**: 即時顯示處理進度，提升用戶體驗
4. **錯誤處理**: 壓縮失敗時自動回退到原始檔案

## 使用建議

1. **圖片**: 建議上傳高品質原圖，系統會自動壓縮到適合的大小
2. **影片**: 建議上傳時長不超過5分鐘的影片
3. **檔案大小**: 系統會自動處理大檔案，但建議避免上傳過大的檔案

## 未來擴展

1. **更多格式支援**: 可以添加對更多圖片和影片格式的支援
2. **自訂設定**: 允許用戶自訂壓縮參數
3. **伺服器端壓縮**: 對於不支援客戶端壓縮的情況，可以添加伺服器端備用方案
4. **WebAssembly**: 使用 FFmpeg.wasm 提供更強大的影片處理能力

## 測試建議

1. 測試不同大小的圖片檔案
2. 測試不同格式的影片檔案
3. 測試網路較慢的環境下的用戶體驗
4. 測試瀏覽器相容性
