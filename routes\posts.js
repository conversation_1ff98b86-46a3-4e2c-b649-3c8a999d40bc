const express = require('express');
const router = express.Router();
const Post = require('../models/Post');
const protect = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');
const { uploadToGCS, deleteFromGCS, getFileNameFromUrl } = require('../config/storage');

// 檔案上傳設定 - 使用記憶體存儲
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB 限制
  }
});

// 取得貼文 (分頁)
router.get('/', async (req, res) => {
  const limit = Number(req.query.limit) || 20;
  const lastId = req.query.lastId;
  const query = {};
  if (lastId && mongoose.Types.ObjectId.isValid(lastId)) {
    query._id = { $lt: new mongoose.Types.ObjectId(lastId) };
  }
  try {
    const posts = await Post.find(query)
      .sort({ _id: -1 })
      .limit(limit)
      .populate('author', 'name avatar')
      .populate('comments.author', 'name avatar');
    const nextLastId = posts.length > 0 ? posts[posts.length - 1]._id : null;
    res.json({ posts, nextLastId });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得貼文失敗' });
  }
});

// 建立貼文（支援直接上傳和傳統上傳）
router.post('/', protect, upload.array('media', 9), async (req, res) => {
  const { content, media: directMedia } = req.body;

  try {
    let media = [];

    // 方案1：直接上傳 - 前端已上傳檔案，只傳送 URL
    if (directMedia && Array.isArray(directMedia)) {
      media = directMedia.map(item => ({
        type: item.type || (item.mimeType && item.mimeType.startsWith('video/') ? 'video' : 'image'),
        url: item.url,
        mimeType: item.mimeType,
      }));
    }
    // 方案2：傳統上傳 - 檔案通過 multer 上傳
    else if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        const ext = path.extname(file.originalname);
        const fileName = `${Date.now()}-${file.fieldname}${ext}`;

        try {
          const publicUrl = await uploadToGCS(file.buffer, fileName, file.mimetype);
          media.push({
            type: file.mimetype.startsWith('video/') ? 'video' : 'image',
            url: publicUrl,
            mimeType: file.mimetype,
          });
        } catch (uploadError) {
          console.error('檔案上傳失敗:', uploadError);
          return res.status(500).json({ message: '檔案上傳失敗' });
        }
      }
    }

    const post = await Post.create({
      author: req.user._id,
      content,
      media,
    });

    await post.populate('author', 'name avatar');
    res.status(201).json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '建立貼文失敗' });
  }
});

// 編輯貼文
router.put('/:id', protect, upload.array('media', 9), async (req, res) => {
  const { content } = req.body;
  const removeMediaIds = JSON.parse(req.body.removeMedia || '[]');
  
  try {
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    // 確保只有貼文作者能編輯
    if (post.author.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限編輯此貼文' });
    }

    // 移除選擇刪除的舊媒體
    for (const mId of removeMediaIds) {
      // 找到對應的媒體項目
      const mediaItem = post.media.find(m => m._id.toString() === mId);
      if (mediaItem) {
        // 從 GCS 刪除檔案
        try {
          const fileName = getFileNameFromUrl(mediaItem.url);
          await deleteFromGCS(fileName);
        } catch (deleteError) {
          console.error('從 GCS 刪除檔案失敗:', deleteError);
          // 繼續執行，不中斷流程
        }
        // 從陣列中移除
        post.media.pull(mediaItem._id);
      }
    }
    // 添加新上傳的媒體
    for (const file of req.files || []) {
      const ext = path.extname(file.originalname);
      const fileName = `${Date.now()}-${file.fieldname}${ext}`;

      try {
        const publicUrl = await uploadToGCS(file.buffer, fileName, file.mimetype);
        post.media.push({
          type: file.mimetype.startsWith('video/') ? 'video' : 'image',
          url: publicUrl,
          mimeType: file.mimetype,
        });
      } catch (uploadError) {
        console.error('檔案上傳失敗:', uploadError);
        return res.status(500).json({ message: '檔案上傳失敗' });
      }
    }

    // 若內容有變更，儲存編輯紀錄
    if (post.content !== content) {
      post.editHistory.push({
        content: post.content,
        editedAt: new Date()
      });
      post.isEdited = true;
      post.content = content;
    }

    await post.save();
    await post.populate('author', 'name avatar');
    await post.populate('comments.author', 'name avatar');
    
    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '編輯貼文失敗' });
  }
});

// 刪除貼文
router.delete('/:id', protect, async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    // 確保只有貼文作者能刪除
    if (post.author.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限刪除此貼文' });
    }

    // 刪除相關的媒體檔案
    for (const media of post.media) {
      try {
        const fileName = getFileNameFromUrl(media.url);
        await deleteFromGCS(fileName);
      } catch (deleteError) {
        console.error('從 GCS 刪除檔案失敗:', deleteError);
        // 繼續執行，不中斷流程
      }
    }

    await Post.deleteOne({ _id: req.params.id });
    res.json({ message: '貼文已刪除' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '刪除貼文失敗' });
  }
});

// 編輯評論
router.put('/:postId/comments/:commentId', protect, async (req, res) => {
  const { content } = req.body;
  
  try {
    const post = await Post.findById(req.params.postId);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    const comment = post.comments.id(req.params.commentId);
    if (!comment) return res.status(404).json({ message: '評論不存在' });

    // 確保只有評論作者能編輯
    if (comment.author.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限編輯此評論' });
    }

    // 若內容有變更，儲存編輯紀錄
    if (comment.content !== content) {
      if (!comment.editHistory) comment.editHistory = [];
      
      comment.editHistory.push({
        content: comment.content,
        editedAt: new Date()
      });
      comment.isEdited = true;
      comment.content = content;
    }

    await post.save();
    await post.populate('comments.author', 'name avatar');
    
    res.json(comment);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '編輯評論失敗' });
  }
});

// 刪除評論
router.delete('/:postId/comments/:commentId', protect, async (req, res) => {
  try {
    const post = await Post.findById(req.params.postId);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    const comment = post.comments.id(req.params.commentId);
    if (!comment) return res.status(404).json({ message: '評論不存在' });

    // 確保只有評論作者能刪除
    if (comment.author.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: '沒有權限刪除此評論' });
    }

    post.comments.pull(req.params.commentId);
    await post.save();
    
    res.json({ message: '評論已刪除' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '刪除評論失敗' });
  }
});

// 點讚 / 取消讚
router.post('/:id/like', protect, async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    const alreadyLiked = post.likes.includes(req.user._id);
    if (alreadyLiked) {
      post.likes.pull(req.user._id);
    } else {
      post.likes.push(req.user._id);
    }
    await post.save();

    res.json({ likes: post.likes.length, isLiked: !alreadyLiked });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '點讚失敗' });
  }
});

// 新增評論
router.post('/:id/comments', protect, async (req, res) => {
  const { content } = req.body;
  try {
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    post.comments.push({ author: req.user._id, content, likes: [] });
    await post.save();
    await post.populate('comments.author', 'name avatar');

    res.status(201).json(post.comments[post.comments.length - 1]);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '評論失敗' });
  }
});

// 評論按讚 / 取消讚
router.post('/:postId/comments/:commentId/like', protect, async (req, res) => {
  try {
    const post = await Post.findById(req.params.postId);
    if (!post) return res.status(404).json({ message: '貼文不存在' });

    const comment = post.comments.id(req.params.commentId);
    if (!comment) return res.status(404).json({ message: '評論不存在' });

    // 如果沒有 likes 陣列，則初始化它
    if (!comment.likes) {
      comment.likes = [];
    }

    const alreadyLiked = comment.likes.includes(req.user._id);
    if (alreadyLiked) {
      comment.likes = comment.likes.filter(id => !id.equals(req.user._id));
    } else {
      comment.likes.push(req.user._id);
    }
    
    await post.save();
    res.json({ likes: comment.likes.length, isLiked: !alreadyLiked });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '評論按讚失敗' });
  }
});

// 取得貼文按讚名單
router.get('/:id/likes', protect, async (req, res) => {
  try {
    const post = await Post.findById(req.params.id).populate('likes', 'name avatar');
    if (!post) return res.status(404).json({ message: '貼文不存在' });
    res.json(post.likes);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得按讚名單失敗' });
  }
});

module.exports = router; 