import { createRouter, createWebHistory } from 'vue-router'
import Wall from '../components/Wall.vue'
import LiveRoom from '../components/LiveRoom.vue'
import LiveRoomList from '../components/LiveRoomList.vue'
import MyProfile from '../components/MyProfile.vue'
import OtherProfile from '../components/OtherProfile.vue'
import PrepareLive from '../components/PrepareLive.vue'

const routes = [
  { path: '/', redirect: '/wall' },
  { path: '/wall', name: 'Wall', component: Wall },
  { path: '/liveroom', name: 'LiveRoom', component: LiveRoom },
  { path: '/liveroomlist', name: 'LiveRoomList', component: LiveRoomList },
  { path: '/myprofile', name: 'MyProfile', component: MyProfile },
  { path: '/profile/:id', name: 'OtherProfile', component: OtherProfile },
  { path: '/preparelive', name: 'PrepareLive', component: PrepareLive },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

export default router 