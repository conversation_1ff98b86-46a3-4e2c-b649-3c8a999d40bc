<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>貼文上傳控制測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .button.enabled {
            background-color: #007bff;
            color: white;
        }
        .button.disabled {
            background-color: #6c757d;
            color: #fff;
            cursor: not-allowed;
            opacity: 0.5;
        }
        .controls {
            margin: 15px 0;
        }
        .controls label {
            display: block;
            margin: 10px 0;
        }
        .controls input[type="checkbox"] {
            margin-right: 8px;
        }
        .file-input {
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>貼文上傳控制功能測試</h1>
    
    <div class="test-container">
        <div class="test-title">測試 1: 基本發布按鈕控制</div>
        <div class="test-description">
            測試發布按鈕在不同狀態下的行為：
            <ul>
                <li>沒有內容和媒體時應該禁用</li>
                <li>有內容時應該啟用</li>
                <li>正在壓縮時應該禁用</li>
                <li>正在上傳時應該禁用</li>
                <li>正在發布時應該禁用</li>
            </ul>
        </div>
        
        <div class="controls">
            <label>
                <input type="text" id="content" placeholder="輸入貼文內容..." style="width: 100%; padding: 8px;">
            </label>
            <label>
                <input type="checkbox" id="hasMedia"> 有選擇媒體檔案
            </label>
            <label>
                <input type="checkbox" id="isCompressing"> 正在壓縮檔案
            </label>
            <label>
                <input type="checkbox" id="isUploading"> 正在上傳檔案
            </label>
            <label>
                <input type="checkbox" id="isPublishing"> 正在發布貼文
            </label>
        </div>
        
        <div id="publishStatus" class="status disabled">發布按鈕: 禁用</div>
        <button id="publishButton" class="button disabled" disabled>發布</button>
    </div>

    <div class="test-container">
        <div class="test-title">測試 2: 影片格式支援檢測</div>
        <div class="test-description">檢測瀏覽器支援的影片格式</div>
        
        <div id="videoFormatSupport"></div>
    </div>

    <div class="test-container">
        <div class="test-title">測試 3: 檔案壓縮模擬</div>
        <div class="test-description">模擬檔案壓縮過程</div>
        
        <input type="file" id="fileInput" class="file-input" accept="image/*,video/*" multiple>
        <div class="progress" style="display: none;">
            <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
        <div id="compressionStatus"></div>
        <button id="simulateCompression" class="button enabled">開始壓縮模擬</button>
    </div>

    <script>
        // 模擬 Vue 的響應式狀態
        const state = {
            content: '',
            hasMedia: false,
            isCompressing: false,
            isUploading: false,
            isPublishing: false
        };

        // 計算發布按鈕是否可用的邏輯（與 Vue 組件中的邏輯相同）
        function canPublish() {
            // 基本條件：必須有內容或媒體
            const hasContent = state.content.trim().length > 0 || state.hasMedia;
            
            // 如果沒有內容，不能發布
            if (!hasContent) return false;
            
            // 如果正在壓縮檔案，不能發布
            if (state.isCompressing) return false;
            
            // 如果正在上傳檔案，不能發布
            if (state.isUploading) return false;
            
            // 如果正在發布，不能重複發布
            if (state.isPublishing) return false;
            
            return true;
        }

        // 更新發布按鈕狀態
        function updatePublishButton() {
            const button = document.getElementById('publishButton');
            const status = document.getElementById('publishStatus');
            const enabled = canPublish();
            
            if (enabled) {
                button.disabled = false;
                button.className = 'button enabled';
                status.className = 'status enabled';
                status.textContent = '發布按鈕: 啟用';
            } else {
                button.disabled = true;
                button.className = 'button disabled';
                status.className = 'status disabled';
                status.textContent = '發布按鈕: 禁用';
            }
        }

        // 檢測影片格式支援
        function checkVideoFormatSupport() {
            const container = document.getElementById('videoFormatSupport');
            const formats = [
                { name: 'MP4 (H.264 + AAC)', mimeType: 'video/mp4;codecs=avc1.42E01E,mp4a.40.2' },
                { name: 'WebM (VP8 + Opus)', mimeType: 'video/webm;codecs=vp8,opus' },
                { name: 'WebM (基本)', mimeType: 'video/webm' }
            ];

            let html = '<h4>瀏覽器支援的影片格式：</h4>';
            
            formats.forEach(format => {
                const supported = typeof MediaRecorder !== 'undefined' && MediaRecorder.isTypeSupported(format.mimeType);
                const statusClass = supported ? 'enabled' : 'disabled';
                const statusText = supported ? '支援' : '不支援';
                html += `<div class="status ${statusClass}">${format.name}: ${statusText}</div>`;
            });

            container.innerHTML = html;
        }

        // 模擬壓縮過程
        function simulateCompression() {
            const files = document.getElementById('fileInput').files;
            if (files.length === 0) {
                alert('請先選擇檔案');
                return;
            }

            const progressContainer = document.querySelector('.progress');
            const progressBar = document.getElementById('progressBar');
            const statusDiv = document.getElementById('compressionStatus');
            const button = document.getElementById('simulateCompression');

            // 開始壓縮
            state.isCompressing = true;
            updatePublishButton();
            
            progressContainer.style.display = 'block';
            button.disabled = true;
            button.className = 'button disabled';
            statusDiv.innerHTML = '<div class="status disabled">正在壓縮檔案...</div>';

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    
                    // 壓縮完成
                    setTimeout(() => {
                        state.isCompressing = false;
                        updatePublishButton();
                        
                        statusDiv.innerHTML = '<div class="status enabled">檔案壓縮完成</div>';
                        button.disabled = false;
                        button.className = 'button enabled';
                        progressContainer.style.display = 'none';
                    }, 500);
                }
                
                progressBar.style.width = progress + '%';
            }, 200);
        }

        // 事件監聽器
        document.getElementById('content').addEventListener('input', (e) => {
            state.content = e.target.value;
            updatePublishButton();
        });

        document.getElementById('hasMedia').addEventListener('change', (e) => {
            state.hasMedia = e.target.checked;
            updatePublishButton();
        });

        document.getElementById('isCompressing').addEventListener('change', (e) => {
            state.isCompressing = e.target.checked;
            updatePublishButton();
        });

        document.getElementById('isUploading').addEventListener('change', (e) => {
            state.isUploading = e.target.checked;
            updatePublishButton();
        });

        document.getElementById('isPublishing').addEventListener('change', (e) => {
            state.isPublishing = e.target.checked;
            updatePublishButton();
        });

        document.getElementById('simulateCompression').addEventListener('click', simulateCompression);

        // 初始化
        updatePublishButton();
        checkVideoFormatSupport();
    </script>
</body>
</html>
