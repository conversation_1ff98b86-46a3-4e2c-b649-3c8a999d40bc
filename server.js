// 引入套件
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const dotenv = require('dotenv');

// 連線資料庫
const connectDB = require('./config/db');

dotenv.config();
connectDB();

const app = express();

// Middleware
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:6666', 'https://liveshow.sotime.app'],
  credentials: true
};
app.use(cors(corsOptions));
app.use(express.json());
app.use(morgan('dev'));

// 靜態檔案服務已移除，現在使用 GCP Storage

// 基本路由
app.get('/', (req, res) => {
  res.send({ message: 'Wall Server API' });
});

// 健康檢查端點
app.get('/api/health', (_, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API 路由
app.use('/api/auth', require('./routes/auth'));
app.use('/api/posts', require('./routes/posts'));
app.use('/api/banners', require('./routes/banners'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/live-rooms', require('./routes/liveRooms'));
app.use('/api/hosts', require('./routes/hosts'));
app.use('/api/tags', require('./routes/tags'));
app.use('/api/rankings', require('./routes/rankings'));
app.use('/api/upload', require('./routes/upload'));

// 啟動伺服器
const PORT = process.env.PORT || 5003;
app.listen(PORT, '0.0.0.0', () => console.log(`Server running on port ${PORT}`)); 