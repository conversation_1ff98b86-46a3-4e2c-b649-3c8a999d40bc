const express = require('express');
const router = express.Router();
const LiveRoom = require('../models/LiveRoom');

// 熱門 tags，預設取前 10 個
router.get('/hot', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const agg = await LiveRoom.aggregate([
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: limit },
      { $project: { _id: 0, tag: '$_id' } },
    ]);
    res.json(agg.map(t => t.tag));
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '取得熱門標籤失敗' });
  }
});

module.exports = router; 