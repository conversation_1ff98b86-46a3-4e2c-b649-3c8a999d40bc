const mongoose = require('mongoose');

const rankingUserSchema = new mongoose.Schema({
  name: String,
  avatar: String,
  badge: String,
  contribution: String,
  giftValue: Number,
});

const rankingSchema = new mongoose.Schema(
  {
    type: { type: String, enum: ['audience', 'streamer'], required: true },
    period: { type: String, enum: ['day', 'week', 'month', 'year'], required: true },
    list: [rankingUserSchema],
  },
  { timestamps: true }
);

rankingSchema.index({ type: 1, period: 1 }, { unique: true });

module.exports = mongoose.model('Ranking', rankingSchema); 