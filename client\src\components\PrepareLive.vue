<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
<div class="live-setup bg-gray-900 min-h-screen text-white">
<!-- 頂部導航欄 -->
<div class="nav-bar fixed top-0 w-full bg-gray-800 shadow-md z-50 px-4 py-3 flex items-center justify-between">
<a href="https://readdy.ai/home/<USER>/cf8ca966-b07a-4694-b08a-f370443026f1" data-readdy="true" class="text-gray-300 flex items-center justify-center cursor-pointer !rounded-button">
<i class="fas fa-arrow-left text-lg"></i>
</a>
<div class="flex-1 flex justify-center">
<span class="text-lg font-medium text-white">開始直播</span>
</div>
<button id="helpButton" @click="toggleHelpDialog" class="text-gray-300 cursor-pointer !rounded-button">
<i class="fas fa-question-circle text-lg"></i>
</button>
</div>

<!-- 幫助對話框 -->
<div v-if="showHelpDialog" class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
  <div class="bg-gray-800 w-full max-w-md rounded-xl overflow-hidden">
    <div class="p-4 border-b border-gray-700 flex justify-between items-center">
      <h3 class="text-lg font-medium">直播幫助指南</h3>
      <button @click="toggleHelpDialog" class="text-gray-400 hover:text-white cursor-pointer !rounded-button">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="p-4 max-h-[70vh] overflow-y-auto">
      <div class="space-y-4">
        <div v-for="(item, index) in helpItems" :key="index" class="help-item">
          <div class="flex items-center mb-2">
            <i :class="['fas', item.icon, 'text-purple-500 mr-3']"></i>
            <h4 class="text-white font-medium">{{ item.title }}</h4>
          </div>
          <p class="text-gray-300 text-sm pl-7">{{ item.description }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 主內容區域 -->
<div class="content-area pt-14 pb-24">
<!-- 攝像頭預覽區域 -->
<div class="camera-preview relative bg-black w-full h-64 md:h-80 shadow-lg">
<div v-if="!cameraActive" class="flex flex-col items-center justify-center h-full">
<i class="fas fa-video-slash text-4xl text-gray-500 mb-3"></i>
<p class="text-gray-400 text-sm">點擊開啟攝像頭預覽</p>
<button @click="activateCamera" class="mt-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full text-sm font-medium cursor-pointer !rounded-button">
開啟攝像頭
</button>
</div>
<video
v-else
ref="videoPreview"
autoplay
muted
class="w-full h-full object-cover"
></video>
<!-- 攝像頭控制按鈕 -->
<div v-if="cameraActive" class="absolute bottom-4 right-4 flex space-x-3">
<button @click="toggleMicrophone" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/80 cursor-pointer !rounded-button">
<i :class="['fas', isMicMuted ? 'fa-microphone-slash text-red-400' : 'fa-microphone text-white']"></i>
</button>
<button @click="switchCamera" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/80 cursor-pointer !rounded-button">
<i class="fas fa-sync-alt text-white"></i>
</button>
<button @click="takeSnapshot" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/80 cursor-pointer !rounded-button">
<i class="fas fa-camera text-white"></i>
</button>
</div>
<!-- 麥克風音量指示器 -->
<div v-if="cameraActive && !isMicMuted" class="absolute bottom-4 left-4 bg-gray-800/80 px-3 py-2 rounded-lg">
<div class="flex items-center space-x-1">
<div v-for="i in 5" :key="i" :class="[
'w-1 rounded-full transition-all duration-200',
i <= audioLevel ? 'bg-green-400' : 'bg-gray-600',
getBarHeight(i)
]"></div>
</div>
</div>
</div>
<!-- 設置區域 -->
<div class="setup-area px-4 mt-4">
<!-- 直播標題 -->
<div class="mb-5">
<label class="block text-sm text-gray-400 mb-2">直播標題</label>
<div class="relative">
<input
v-model="streamTitle"
type="text"
placeholder="為你的直播添加吸引人的標題..."
class="w-full px-4 py-3 bg-gray-800 rounded-lg text-white border-none focus:ring-2 focus:ring-purple-500 outline-none"
maxlength="50"
/>
<span class="absolute right-3 bottom-3 text-xs text-gray-400">{{ streamTitle.length }}/50</span>
</div>
</div>
<!-- 直播分類 -->
<div class="mb-5">
<label class="block text-sm text-gray-400 mb-2">直播分類</label>
<div
@click="toggleCategorySelector"
class="flex justify-between items-center w-full px-4 py-3 bg-gray-800 rounded-lg cursor-pointer !rounded-button"
>
<div class="flex items-center">
<i :class="['fas mr-3 text-lg', categoryIcons[selectedCategory] || 'fa-list']"></i>
<span>{{ categoryNames[selectedCategory] || '選擇分類' }}</span>
</div>
<i class="fas fa-chevron-down text-gray-400"></i>
</div>
<!-- 分類選擇器彈出層 -->
<div v-if="showCategorySelector" class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4" @click.self="showCategorySelector = false">
<div class="bg-gray-800 w-full max-w-md rounded-xl overflow-hidden">
<div class="p-4 border-b border-gray-700 flex justify-between items-center">
<h3 class="text-lg font-medium">選擇直播分類</h3>
<button @click="showCategorySelector = false" class="text-gray-400 cursor-pointer !rounded-button">
<i class="fas fa-times"></i>
</button>
</div>
<div class="max-h-96 overflow-y-auto">
<div
v-for="(name, category) in categoryNames"
:key="category"
@click="selectCategory(category)"
class="flex items-center px-4 py-3 hover:bg-gray-700 cursor-pointer"
>
<i :class="['fas mr-3 text-lg', categoryIcons[category] || 'fa-list']"></i>
<span>{{ name }}</span>
<i v-if="selectedCategory === category" class="fas fa-check ml-auto text-purple-500"></i>
</div>
</div>
</div>
</div>
</div>
<!-- 標籤添加 -->
<div class="mb-5">
<label class="block text-sm text-gray-400 mb-2">標籤</label>
<div class="flex flex-wrap gap-2 mb-2">
<div
v-for="(tag, index) in tags"
:key="index"
class="px-3 py-1.5 bg-gray-700 rounded-full text-sm flex items-center"
>
<span>{{ tag }}</span>
<button @click="removeTag(index)" class="ml-2 text-gray-400 hover:text-gray-200 cursor-pointer !rounded-button">
<i class="fas fa-times text-xs"></i>
</button>
</div>
</div>
<div class="flex">
<input
v-model="newTag"
type="text"
placeholder="添加標籤..."
class="flex-1 px-4 py-3 bg-gray-800 rounded-l-lg text-white border-none focus:ring-2 focus:ring-purple-500 outline-none"
@keyup.enter="addTag"
/>
<button
@click="addTag"
class="px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-r-lg cursor-pointer !rounded-button"
>
<i class="fas fa-plus"></i>
</button>
</div>
<p class="mt-1 text-xs text-gray-400">最多添加5個標籤，使用標籤讓觀眾更容易找到你</p>
</div>
<!-- 直播封面 -->
<div class="mb-5">
<label class="block text-sm text-gray-400 mb-2">直播封面</label>
<div class="grid grid-cols-3 gap-3">
<div
:class="['relative aspect-video rounded-lg overflow-hidden cursor-pointer',
coverSource === 'upload' && !uploadedCover ? 'bg-gray-800 border-2 border-dashed border-gray-600 flex items-center justify-center' : '']"
@click="triggerFileInput"
>
<input
ref="fileInput"
type="file"
accept="image/*"
class="hidden"
@change="handleFileUpload"
/>
<template v-if="coverSource === 'upload' && !uploadedCover">
<i class="fas fa-upload text-gray-400 text-xl"></i>
</template>
<img v-else-if="uploadedCover" :src="uploadedCover" alt="上傳封面" class="w-full h-full object-cover" />
<div v-if="coverSource === 'upload'" class="absolute bottom-0 left-0 right-0 bg-black/50 py-1 text-center text-xs">
上傳
</div>
</div>
<div
:class="['relative aspect-video rounded-lg overflow-hidden cursor-pointer',
coverSource === 'snapshot' && !snapshotCover ? 'bg-gray-800 border-2 border-dashed border-gray-600 flex items-center justify-center' : '']"
@click="coverSource = 'snapshot'"
>
<template v-if="coverSource === 'snapshot' && !snapshotCover">
<i class="fas fa-camera text-gray-400 text-xl"></i>
</template>
<img v-else-if="snapshotCover" :src="snapshotCover" alt="截圖封面" class="w-full h-full object-cover" />
<div v-if="coverSource === 'snapshot'" class="absolute bottom-0 left-0 right-0 bg-black/50 py-1 text-center text-xs">
截圖
</div>
</div>
<div
:class="['relative aspect-video rounded-lg overflow-hidden cursor-pointer',
coverSource === 'template' ? 'border-2 border-purple-500' : '']"
@click="coverSource = 'template'"
>
<img src="https://readdy.ai/api/search-image?query=Professional%20streaming%20setup%20with%20camera%2C%20microphone%2C%20and%20soft%20lighting%2C%20cozy%20gaming%20room%20with%20RGB%20lighting%2C%20streaming%20equipment%20visible%2C%20high%20quality%20photography%2C%20modern%20gaming%20setup%2C%20clean%20composition%2C%20vibrant%20colors%2C%20detailed%20equipment&width=400&height=225&seq=23&orientation=landscape" alt="模板封面" class="w-full h-full object-cover" />
<div class="absolute bottom-0 left-0 right-0 bg-black/50 py-1 text-center text-xs">
模板
</div>
</div>
</div>
</div>
<!-- 直播設置 -->
<div class="mb-5">
<label class="block text-sm text-gray-400 mb-2">直播設置</label>
<div class="bg-gray-800 rounded-lg overflow-hidden">
<div class="setting-item px-4 py-3 flex justify-between items-center border-b border-gray-700">
<div class="flex items-center">
<i class="fas fa-video text-gray-400 mr-3"></i>
<span>畫質設置</span>
</div>
<div class="flex items-center">
<span class="text-sm text-gray-400 mr-2">{{ qualityOptions[selectedQuality] }}</span>
<i class="fas fa-chevron-right text-gray-500 text-sm"></i>
</div>
</div>
<div class="setting-item px-4 py-3 flex justify-between items-center border-b border-gray-700">
<div class="flex items-center">
<i class="fas fa-lock text-gray-400 mr-3"></i>
<span>隱私設置</span>
</div>
<div class="flex items-center">
<span class="text-sm text-gray-400 mr-2">{{ privacyOptions[selectedPrivacy] }}</span>
<i class="fas fa-chevron-right text-gray-500 text-sm"></i>
</div>
</div>
<div class="setting-item px-4 py-3 flex justify-between items-center">
<div class="flex items-center">
<i class="fas fa-comment-alt text-gray-400 mr-3"></i>
<span>聊天設置</span>
</div>
<div class="flex items-center">
<span class="text-sm text-gray-400 mr-2">所有人可聊天</span>
<i class="fas fa-chevron-right text-gray-500 text-sm"></i>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 底部開始直播按鈕 -->
<div class="fixed bottom-0 left-0 right-0 bg-gray-800 px-4 py-3 shadow-lg z-40">
<button
id="startLiveButton"
:disabled="!isFormValid"
@click="showConfirmDialog"
:class="['w-full py-3.5 rounded-lg font-medium text-white text-center cursor-pointer !rounded-button',
isFormValid ? 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500' : 'bg-gray-700 text-gray-400']"
>
開始直播
</button>
</div>
<!-- 確認對話框 -->
<div v-if="showDialog" class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
<div class="bg-gray-800 w-full max-w-md rounded-xl overflow-hidden">
<div class="p-4 border-b border-gray-700">
<h3 class="text-lg font-medium">確認開始直播</h3>
</div>
<div class="p-4">
<div class="space-y-3">
<div class="flex items-center">
<i class="fas fa-video text-gray-400 w-6"></i>
<span class="text-gray-300">{{ streamTitle }}</span>
</div>
<div class="flex items-center">
<i :class="['fas w-6 text-gray-400', categoryIcons[selectedCategory]]"></i>
<span class="text-gray-300">{{ categoryNames[selectedCategory] }}</span>
</div>
<div class="flex items-center">
<i class="fas fa-lock text-gray-400 w-6"></i>
<span class="text-gray-300">{{ privacyOptions[selectedPrivacy] }}</span>
</div>
</div>
</div>
<div class="p-4 bg-gray-900 flex space-x-3">
<button
@click="cancelDialog"
class="flex-1 py-2.5 rounded-lg bg-gray-700 text-white font-medium cursor-pointer !rounded-button"
>
取消
</button>
<button
@click="startCountdown"
class="flex-1 py-2.5 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white font-medium cursor-pointer !rounded-button"
>
開始直播
</button>
</div>
</div>
</div>
<!-- 倒計時遮罩 -->
<div v-if="showCountdown" class="fixed inset-0 bg-black/90 z-50 flex items-center justify-center">
<div class="text-center">
<div class="text-7xl font-bold text-white mb-4">{{ countdownNumber }}</div>
<div class="text-gray-400">即將開始直播...</div>
</div>
</div>
<!-- 直播開始提示 -->
<div v-if="showStartedNotification" class="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg z-50 flex items-center">
<i class="fas fa-check-circle mr-2"></i>
直播已開始
</div>
</div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
// 對話框相關狀態
const showDialog = ref(false);
const showCountdown = ref(false);
const countdownNumber = ref(3);
const showStartedNotification = ref(false);
// 顯示確認對話框
const showConfirmDialog = () => {
showDialog.value = true;
};
// 取消對話框
const cancelDialog = () => {
showDialog.value = false;
};
// 開始倒計時
const startCountdown = () => {
showDialog.value = false;
showCountdown.value = true;
countdownNumber.value = 3;
const countdownInterval = setInterval(() => {
countdownNumber.value--;
if (countdownNumber.value === 0) {
clearInterval(countdownInterval);
setTimeout(() => {
showCountdown.value = false;
startLiveStream();
}, 1000);
}
}, 1000);
};
// 開始直播
const startLiveStream = () => {
showStartedNotification.value = true;
setTimeout(() => {
showStartedNotification.value = false;
}, 3000);
};
// 攝像頭相關
const videoPreview = ref<HTMLVideoElement | null>(null);
const cameraActive = ref(false);
const isMicMuted = ref(false);
const audioLevel = ref(0);
let audioContext: AudioContext | null = null;
let analyser: AnalyserNode | null = null;
let microphone: MediaStreamAudioSourceNode | null = null;
let animationFrame: number | null = null;
let mediaStream: MediaStream | null = null;
// 直播表單數據
const streamTitle = ref('');
const selectedCategory = ref('');
const tags = ref<string[]>([]);
const newTag = ref('');
const coverSource = ref('template');
const uploadedCover = ref('');
const snapshotCover = ref('');
const fileInput = ref<HTMLInputElement | null>(null);

// 幫助對話框相關
const showHelpDialog = ref(false);
const helpItems = [
  {
    icon: 'fa-video',
    title: '攝像頭設置',
    description: '點擊開啟攝像頭按鈕開始預覽。你可以切換前後攝像頭，調整麥克風，或拍攝快照作為直播封面。'
  },
  {
    icon: 'fa-heading',
    title: '直播標題',
    description: '為你的直播設置一個吸引人的標題，最多50個字符。好的標題能幫助更多觀眾找到你的直播。'
  },
  {
    icon: 'fa-list',
    title: '選擇分類',
    description: '選擇最適合你直播內容的分類，這將幫助你的直播內容觸達目標觀眾。'
  },
  {
    icon: 'fa-tags',
    title: '添加標籤',
    description: '最多可添加5個標籤，使用標籤來描述你的直播內容，讓觀眾更容易找到你。'
  },
  {
    icon: 'fa-image',
    title: '直播封面',
    description: '你可以上傳圖片、使用攝像頭拍攝或選擇預設模板作為直播封面。好的封面能提高直播間的點擊率。'
  },
  {
    icon: 'fa-cog',
    title: '直播設置',
    description: '調整畫質、隱私和聊天設置，為你的直播提供最佳體驗。'
  }
];

// 切換幫助對話框
const toggleHelpDialog = () => {
  showHelpDialog.value = !showHelpDialog.value;
};
// 設置選項
const selectedQuality = ref('hd');
const selectedPrivacy = ref('public');
const showCategorySelector = ref(false);
// 分類選項
const categoryNames = {
game: '遊戲',
chat: '聊天',
music: '音樂',
food: '美食',
outdoor: '戶外',
dance: '舞蹈',
art: '藝術',
sports: '運動',
tech: '科技',
education: '教育'
};
const categoryIcons = {
game: 'fa-gamepad',
chat: 'fa-comments',
music: 'fa-music',
food: 'fa-utensils',
outdoor: 'fa-mountain',
dance: 'fa-music',
art: 'fa-palette',
sports: 'fa-running',
tech: 'fa-laptop-code',
education: 'fa-graduation-cap'
};
const qualityOptions = {
auto: '自動',
hd: '高清 (720p)',
fullhd: '全高清 (1080p)',
low: '流暢 (480p)'
};
const privacyOptions = {
public: '公開',
followers: '僅關注者',
private: '私密'
};
// 表單驗證
const isFormValid = computed(() => {
return streamTitle.value.trim().length > 0 && selectedCategory.value !== '';
});
// 切換分類選擇器
const toggleCategorySelector = () => {
showCategorySelector.value = !showCategorySelector.value;
};
// 選擇分類
const selectCategory = (category: string) => {
selectedCategory.value = category;
showCategorySelector.value = false;
};
// 添加標籤
const addTag = () => {
const tag = newTag.value.trim();
if (tag && tags.value.length < 5 && !tags.value.includes(tag)) {
tags.value.push(tag);
newTag.value = '';
}
};
// 移除標籤
const removeTag = (index: number) => {
tags.value.splice(index, 1);
};
// 觸發文件選擇
const triggerFileInput = () => {
coverSource.value = 'upload';
if (fileInput.value) {
fileInput.value.click();
}
};
// 處理文件上傳
const handleFileUpload = (event: Event) => {
const input = event.target as HTMLInputElement;
if (input.files && input.files[0]) {
const file = input.files[0];
const reader = new FileReader();
reader.onload = (e) => {
if (e.target && typeof e.target.result === 'string') {
uploadedCover.value = e.target.result;
}
};
reader.readAsDataURL(file);
}
};
// 開啟攝像頭
const activateCamera = async () => {
try {
mediaStream = await navigator.mediaDevices.getUserMedia({
video: true,
audio: true
});
if (videoPreview.value) {
videoPreview.value.srcObject = mediaStream;
}
cameraActive.value = true;
setupAudioAnalyser(mediaStream);
// 開始音量監測
updateAudioLevel();
} catch (error) {
console.error('無法訪問攝像頭或麥克風:', error);
}
};
// 設置音頻分析器
const setupAudioAnalyser = (stream: MediaStream) => {
audioContext = new AudioContext();
analyser = audioContext.createAnalyser();
analyser.fftSize = 256;
microphone = audioContext.createMediaStreamSource(stream);
microphone.connect(analyser);
// 不連接到音頻輸出，避免回音
// analyser.connect(audioContext.destination);
};
// 更新音量等級
const updateAudioLevel = () => {
if (!analyser || isMicMuted.value) {
audioLevel.value = 0;
animationFrame = requestAnimationFrame(updateAudioLevel);
return;
}
const dataArray = new Uint8Array(analyser.frequencyBinCount);
analyser.getByteFrequencyData(dataArray);
// 計算平均音量
let sum = 0;
for (let i = 0; i < dataArray.length; i++) {
sum += dataArray[i];
}
const average = sum / dataArray.length;
// 將平均值映射到1-5的範圍
audioLevel.value = Math.min(5, Math.ceil(average / 51));
animationFrame = requestAnimationFrame(updateAudioLevel);
};
// 切換麥克風靜音
const toggleMicrophone = () => {
if (mediaStream) {
const audioTracks = mediaStream.getAudioTracks();
if (audioTracks.length > 0) {
audioTracks[0].enabled = isMicMuted.value;
isMicMuted.value = !isMicMuted.value;
}
}
};
// 切換攝像頭
const switchCamera = async () => {
if (mediaStream) {
mediaStream.getTracks().forEach(track => track.stop());
try {
mediaStream = await navigator.mediaDevices.getUserMedia({
video: { facingMode: 'environment' },
audio: true
});
if (videoPreview.value) {
videoPreview.value.srcObject = mediaStream;
}
setupAudioAnalyser(mediaStream);
} catch (error) {
console.error('切換攝像頭失敗:', error);
}
}
};
// 截取快照作為封面
const takeSnapshot = () => {
if (videoPreview.value && cameraActive.value) {
const canvas = document.createElement('canvas');
canvas.width = videoPreview.value.videoWidth;
canvas.height = videoPreview.value.videoHeight;
const ctx = canvas.getContext('2d');
if (ctx) {
ctx.drawImage(videoPreview.value, 0, 0, canvas.width, canvas.height);
snapshotCover.value = canvas.toDataURL('image/jpeg');
coverSource.value = 'snapshot';
}
}
};
// 獲取音量條高度
const getBarHeight = (index: number) => {
const heights = ['h-2', 'h-3', 'h-4', 'h-5', 'h-6'];
return heights[index - 1] || 'h-2';
};
// 組件掛載
onMounted(() => {
// 初始化默認標籤
if (tags.value.length === 0) {
tags.value = ['直播', '新手上路'];
}
});
// 組件卸載
onUnmounted(() => {
// 清理資源
if (animationFrame) {
cancelAnimationFrame(animationFrame);
}
if (mediaStream) {
mediaStream.getTracks().forEach(track => track.stop());
}
if (audioContext) {
audioContext.close();
}
});
</script>
<style scoped>
.setting-item:hover {
background-color: rgba(75, 85, 99, 0.3);
}

.help-item {
  transition: all 0.2s ease;
}

.help-item:hover {
  transform: translateX(4px);
}
input[type="text"]:focus {
outline: none;
}
/* 隱藏 input number 的箭頭 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
input[type="number"] {
-moz-appearance: textfield;
}
/* 確保內容區域不被底部按鈕遮擋 */
.content-area {
padding-bottom: 80px;
}
</style>
