/**
 * 統一的檔案壓縮工具
 * 整合圖片和影片壓縮功能
 */

import { compressImage, compressImages, type CompressionOptions, type CompressionResult } from './imageCompression';
import { compressVideo, getVideoInfo, isVideoCompressionSupported, getBestSupportedVideoFormat, type VideoCompressionOptions, type VideoCompressionResult } from './videoCompression';

export interface FileCompressionOptions {
  // 圖片壓縮選項
  image?: CompressionOptions;
  // 影片壓縮選項
  video?: VideoCompressionOptions;
  // 是否啟用壓縮
  enabled?: boolean;
}

export interface FileCompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  type: 'image' | 'video';
  error?: string;
}

export interface CompressionProgress {
  current: number;
  total: number;
  currentFile: string;
  progress: number; // 0-100
  stage: 'analyzing' | 'compressing' | 'complete';
}

/**
 * 預設壓縮設定
 */
const DEFAULT_OPTIONS: FileCompressionOptions = {
  enabled: true,
  image: {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8,
    format: 'jpeg',
    maxSizeKB: 1024 // 1MB
  },
  video: {
    maxWidth: 1280,
    maxHeight: 720,
    quality: 0.7,
    maxSizeKB: 10240, // 10MB
    maxDuration: 300, // 5分鐘
    videoBitrate: 1000000, // 1Mbps
    audioBitrate: 128000 // 128kbps
  }
};

/**
 * 壓縮單個檔案
 */
export async function compressFile(
  file: File,
  options: FileCompressionOptions = {},
  onProgress?: (progress: number) => void
): Promise<FileCompressionResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (!opts.enabled) {
    return {
      file,
      originalSize: file.size,
      compressedSize: file.size,
      compressionRatio: 0,
      type: file.type.startsWith('video/') ? 'video' : 'image'
    };
  }

  try {
    if (file.type.startsWith('image/')) {
      const result = await compressImage(file, opts.image);
      return {
        file: result.file,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        type: 'image'
      };
    } else if (file.type.startsWith('video/')) {
      if (!isVideoCompressionSupported()) {
        console.warn('瀏覽器不支援影片壓縮');
        return {
          file,
          originalSize: file.size,
          compressedSize: file.size,
          compressionRatio: 0,
          type: 'video',
          error: '瀏覽器不支援影片壓縮'
        };
      }

      const result = await compressVideo(file, opts.video, onProgress);
      return {
        file: result.file,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        type: 'video'
      };
    } else {
      throw new Error('不支援的檔案類型');
    }
  } catch (error) {
    console.error('檔案壓縮失敗:', error);
    return {
      file,
      originalSize: file.size,
      compressedSize: file.size,
      compressionRatio: 0,
      type: file.type.startsWith('video/') ? 'video' : 'image',
      error: error instanceof Error ? error.message : '壓縮失敗'
    };
  }
}

/**
 * 批量壓縮檔案
 */
export async function compressFiles(
  files: File[],
  options: FileCompressionOptions = {},
  onProgress?: (progress: CompressionProgress) => void
): Promise<FileCompressionResult[]> {
  const results: FileCompressionResult[] = [];
  const opts = { ...DEFAULT_OPTIONS, ...options };

  if (!opts.enabled) {
    return files.map(file => ({
      file,
      originalSize: file.size,
      compressedSize: file.size,
      compressionRatio: 0,
      type: file.type.startsWith('video/') ? 'video' : 'image'
    }));
  }

  // 分離圖片和影片
  const imageFiles = files.filter(f => f.type.startsWith('image/'));
  const videoFiles = files.filter(f => f.type.startsWith('video/'));

  let processedCount = 0;
  const totalFiles = files.length;

  // 批量處理圖片
  if (imageFiles.length > 0) {
    if (onProgress) {
      onProgress({
        current: processedCount,
        total: totalFiles,
        currentFile: '正在壓縮圖片...',
        progress: 0,
        stage: 'compressing'
      });
    }

    try {
      const imageResults = await compressImages(
        imageFiles,
        opts.image,
        (progress, currentFile) => {
          if (onProgress) {
            onProgress({
              current: processedCount,
              total: totalFiles,
              currentFile,
              progress,
              stage: 'compressing'
            });
          }
        }
      );

      results.push(...imageResults.map(result => ({
        file: result.file,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        type: 'image' as const
      })));

      processedCount += imageFiles.length;
    } catch (error) {
      console.error('批量圖片壓縮失敗:', error);
      // 添加原始檔案
      results.push(...imageFiles.map(file => ({
        file,
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: 0,
        type: 'image' as const,
        error: '壓縮失敗'
      })));
      processedCount += imageFiles.length;
    }
  }

  // 逐個處理影片
  for (let i = 0; i < videoFiles.length; i++) {
    const file = videoFiles[i];
    
    if (onProgress) {
      onProgress({
        current: processedCount,
        total: totalFiles,
        currentFile: file.name,
        progress: 0,
        stage: 'compressing'
      });
    }

    try {
      const result = await compressFile(
        file,
        opts,
        (progress) => {
          if (onProgress) {
            onProgress({
              current: processedCount,
              total: totalFiles,
              currentFile: file.name,
              progress,
              stage: 'compressing'
            });
          }
        }
      );
      results.push(result);
    } catch (error) {
      console.error(`影片 ${file.name} 壓縮失敗:`, error);
      results.push({
        file,
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: 0,
        type: 'video',
        error: error instanceof Error ? error.message : '壓縮失敗'
      });
    }

    processedCount++;
  }

  if (onProgress) {
    onProgress({
      current: totalFiles,
      total: totalFiles,
      currentFile: '完成',
      progress: 100,
      stage: 'complete'
    });
  }

  return results;
}

/**
 * 檢查檔案是否需要壓縮
 */
export function shouldCompress(file: File, options: FileCompressionOptions = {}): boolean {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (!opts.enabled) return false;

  if (file.type.startsWith('image/')) {
    const maxSize = (opts.image?.maxSizeKB || 1024) * 1024;
    return file.size > maxSize;
  } else if (file.type.startsWith('video/')) {
    const maxSize = (opts.video?.maxSizeKB || 10240) * 1024;
    return file.size > maxSize;
  }

  return false;
}

/**
 * 獲取壓縮統計資訊
 */
export function getCompressionStats(results: FileCompressionResult[]): {
  totalOriginalSize: number;
  totalCompressedSize: number;
  totalSaved: number;
  averageCompressionRatio: number;
  successCount: number;
  errorCount: number;
} {
  const totalOriginalSize = results.reduce((sum, r) => sum + r.originalSize, 0);
  const totalCompressedSize = results.reduce((sum, r) => sum + r.compressedSize, 0);
  const totalSaved = totalOriginalSize - totalCompressedSize;
  const averageCompressionRatio = results.length > 0 
    ? results.reduce((sum, r) => sum + r.compressionRatio, 0) / results.length 
    : 0;
  const successCount = results.filter(r => !r.error).length;
  const errorCount = results.filter(r => r.error).length;

  return {
    totalOriginalSize,
    totalCompressedSize,
    totalSaved,
    averageCompressionRatio,
    successCount,
    errorCount
  };
}

// 導出格式化函數和工具函數
export { formatFileSize } from './imageCompression';
export { formatDuration, getBestSupportedVideoFormat } from './videoCompression';
